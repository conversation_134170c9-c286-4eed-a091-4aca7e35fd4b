{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.8", "dayjs": "^1.11.10", "element-plus": "^2.6.1", "file-saver": "^2.0.5", "jit-auth": "^1.0.3", "lodash-es": "^4.17.21", "pdf-vue3": "^1.0.12", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "prettier": "^3.0.3", "sass": "^1.72.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.5", "vite-plugin-vue-devtools": "^7.0.16"}}