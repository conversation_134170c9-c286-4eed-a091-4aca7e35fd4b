import ZwbhQueryView from '../views/zwbh/views/ZwbhQuery.vue'
import ZwbhFormView from '../views/zwbh/views/ZwbhForm.vue'

const zwbhRouter = [
  {
    path: '/zwbh',
    name: 'zwbh',
    component: ZwbhQueryView,
    meta: {
      title: '指纹保护查询',
      requiresAuth: true
    }
  },
  {
    path: '/zwbhForm',
    name: 'zwbhForm',
    component: ZwbhFormView,
    meta: {
      title: '指纹保护详情',
      requiresAuth: true
    }
  }
]

export default zwbhRouter
