import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/baxz/views/HomeView.vue'
import LoginView from '../views/login/LoginView.vue'
import SM4Test from '../views/test/SM4Test.vue'
import { useAuthStore } from '@/stores/auth'
import baxzRouter from './baxzRouter'
import jdywRouter from './jdywRouter'
import bqglRouter from './bqglRouter'
import bdjgRouter from './bdjgRouter'
import jxsRouter from './JsxRouter'
import zwbhRouter from './ZwbhRouter'
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/main',
      name: 'main',
      component: HomeView
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/sm4-test',
      name: 'sm4-test',
      component: SM4Test
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue')
    },
    ...baxzRouter,
    ...jdywRouter,
    ...bqglRouter,
    ...bdjgRouter,
    ...jxsRouter,
    ...zwbhRouter
  ]
})

router.beforeEach(async (to, from, next) => {
  console.log('to', to.query)

  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title
  }

  // 先从本地存储中获取token
  const token = sessionStorage.getItem('token')
  const accessToken = sessionStorage.getItem('access_token')

  const authStore = useAuthStore()
  if (to.path === '/login') {
    next()
  } else if (authStore.isAuthenticated) {
    // 如果是已经认证过则放行
    next()
  } else if (token || to.query.token || accessToken || to.query.access_token || to.query.sm4Code) {
    //判断如果URL上带有token 或者本地缓存有token 或者带有access_token 或者本地缓存有access_token 或者带有sm4Code 则获取这个token 请求一遍获取用户信息(无论如何都要请求一遍)

    if (to.query.sm4Code) {
      // 如果URL上带有sm4Code，则通过sm4Code获取token并获取用户信息
      console.log('sm4Code', to.query.sm4Code)
      try {
        await authStore.getUserInfoBySM4Code(to.query.sm4Code)
      } catch (error) {
        console.error('通过SM4代码认证失败:', error)
        // 清除本地缓存，和用户数据
        sessionStorage.removeItem('token')
        sessionStorage.removeItem('access_token')
        sessionStorage.removeItem('userInfo')
        authStore.clearUserInfo()
        next({ name: 'login' })
        return
      }
    } else {
      // 原有的token处理逻辑
      const _token = token || to.query.token || accessToken || to.query.access_token
      console.log('_token', _token)
      await authStore.getUserInfoByToken(_token)
    }

    console.log(authStore.isAuthenticated)
    if (authStore.isAuthenticated) {
      // sessionStorage.setItem('token', token)
      next()
    } else {
      // 清除本地缓存，和用户数据
      sessionStorage.removeItem('token')
      sessionStorage.removeItem('access_token')
      sessionStorage.removeItem('userInfo')
      authStore.clearUserInfo()
      next({ name: 'login' })
    }
  } else {
    // 如果什么都没有，那就走认证
    next({ name: 'login' })
  }
  // 如果路由需要鉴权
  // if (to.meta.requiresAuth) {
  //   // check if user is logged in
  //   // if not, redirect to login page
  //   if (!sessionStorage.getItem('token')) {
  //     next({ name: 'login' })
  //   } else {
  //     next()
  //   }
  // } else {
  //   next()
  // }
})

export default router
