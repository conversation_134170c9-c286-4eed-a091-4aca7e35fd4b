import JsxQueryView from '../views/jsx/views/JsxQuery.vue'
import JsxFormView from '../views/jsx/views/JsxForm.vue'

const jxsRouter = [
  {
    path: '/jsx',
    name: 'jsx',
    component: JsxQueryView,
    meta: {
      title: '介绍信管理',
      requiresAuth: true
    }
  },
  {
    path: '/jsxForm',
    name: 'jsxForm',
    component: JsxFormView,
    meta: {
      title: '介绍信表单',
      requiresAuth: true
    }
  }
]

export default jxsRouter
