package cn.sinobest.jzpt.jd.util;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.security.Security;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

//import com.nuts.ibd.utils.LogUtils;

/**
 * SM4加解密工具类
 *
 * <AUTHOR>
 * @className SM4Utils
 * @date 2021/2/19 10:03
 */
public class SM4Utils
{
    static
    {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String ENCODING = "UTF-8";
    public static final String ALGORITHE_NAME = "SM4";
    /**
     * 加密算法/分组加密模式/分组填充方式
     * PKCS5Padding 以8个字节为一组进行分组隔离
     * 定义分组加密模式使用：PKCS5Padding
     */
    public static final String ALGORITHE_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    //默认key大小
    public static final int DEFAULT_KEY_SIZE = 128;
    //默认key
    public static final String DEFAULT_KEY = "46c63180c2806E1F47B859DE20210301";

    /**
     * 生成ECB暗号
     *
     * <AUTHOR>
     * @param algorithmName 算法名称
     * @param mode 模式
     * @param key 秘钥
     * @exception NoSuchAlgorithmException 异常
     * @exception NoSuchProviderException 异常
     * @exception NoSuchPaddingException 异常
     * @exception InvalidKeyException 异常
     * @return javax.crypto.Cipher
     * @date 2021/2/19 10:17
     */

    private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key)throws NoSuchAlgorithmException,
            NoSuchProviderException, NoSuchPaddingException, InvalidKeyException
    {
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHE_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }

    /**
     * 获取公鈅(可指定公钥大小（256/128）)
     *
     * <AUTHOR>
     * @param keySize keySize
     * @return 返回结果
     * @throws NoSuchAlgorithmException 异常
     * @throws NoSuchProviderException 异常
     */

    public static byte[] generateKey(int keySize) throws NoSuchAlgorithmException, NoSuchProviderException
    {
        //获取SM4公鈅
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHE_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

    /**
     * 获取公鈅(默认公钥大小为128bit)
     *
     * <AUTHOR>
     * @return 返回结果
     * @throws NoSuchAlgorithmException 异常
     * @throws NoSuchProviderException 异常
     */

    public static String generateKey() throws NoSuchAlgorithmException, NoSuchProviderException
    {
        return ByteUtils.toHexString(generateKey(DEFAULT_KEY_SIZE));
    }

    /**
     * sm4解密
     *
     * <AUTHOR>
     * @param hexKey 16进制秘钥
     * @param cipherText 16进制加密后的字符串
     * @return 解密后的数据
     * @exception NoSuchAlgorithmException 异常
     * @exception NoSuchProviderException 异常
     * @exception NoSuchPaddingException 异常
     * @exception InvalidKeyException 异常
     * @exception UnsupportedEncodingException 异常
     * @exception IllegalBlockSizeException 异常
     * @exception BadPaddingException 异常
     * @date 2021/2/19 10:33
     */

    public static String decrypt(String hexKey, String  cipherText)throws NoSuchAlgorithmException, NoSuchProviderException,
            NoSuchPaddingException, InvalidKeyException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException
    {
        byte[]keyData = ByteUtils.fromHexString(hexKey);
        byte[]cipherData = ByteUtils.fromHexString(cipherText);
        byte[]srcData = generateEcbCipher(ALGORITHE_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, keyData).doFinal(cipherData);
        return new String(srcData, ENCODING);
    }

    public static String decrypt(String cipherText)throws NoSuchAlgorithmException, NoSuchProviderException,
            NoSuchPaddingException, InvalidKeyException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException
    {
        return decrypt(DEFAULT_KEY, cipherText);
    }


    public static void main(String[] args){

//        测试用户：userid：gymj01 ;  username: 公园民警01 ；sfz: 450205198811040432；
        String dd = "gymj01,公园民警01,450205198811040432,450721000022";
        String ddc = "8156068636612663f40b606b40db000a241db109bb859b55efe4cfe569bbd189b0f7e8930259cbeb7dede2dadc212d2e76e127e3f06569f949203f43b3f972f9";


        try {
            String data = SM4Utils.decrypt(ddc);
            String dataS = data.split(",")[2];
            System.out.println("data== "+data);
            System.out.println("dataS== "+dataS);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchProviderException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        }


    }

}
