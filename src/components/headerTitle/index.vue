<template>
  <div class="title-container">
    <span class="title">{{ title }}</span>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '标题'
  }
})
</script>

<style lang="less" scoped>
.title-container {
  width: 100%;
  color: #fff;
  height: 40px;
  background-color: @themeColor;
}
.title {
  line-height: 40px;
  letter-spacing: 0.05em;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  padding-left: 40px;
  font-size: 20px;
}
</style>
