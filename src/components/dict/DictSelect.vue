<template>
  <el-select
    v-model="selectedValue"
    filterable
    clearable
    remote
    reserve-keyword
    remote-show-suffix
    placeholder="请选择"
    :remote-method="remoteSearch"
    :loading="loading"
    @compositionstart="handleCompositionStart"
    @compositionend="handleCompositionEnd"
  >
    <el-option v-for="item in dictData" :key="item.code" :label="item.detail" :value="item.code">
      <span style="float: left">{{
        props.showCode ? `${item.code} | ${item.detail}` : item.detail
      }}</span>
    </el-option>
    <template #empty>
      <span v-if="!loading">无匹配数据</span>
    </template>
    <template #footer>
      <el-pagination
        v-model:currentPage="currentPage"
        :page-count="totalPages"
        :page-size="pageSize"
        layout="prev, pager, next"
        @current-change="handlePageChange"
      />
    </template>
  </el-select>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRequest } from '@/utils/request/useRequest'
import { debounce } from 'lodash-es'

const props = defineProps({
  kind: {
    type: String,
    required: false
  },
  params: {
    type: Object,
    required: false,
    default: () => ({})
  },
  type: {
    type: String,
    required: false,
    default: 'static'
  },
  showCode: {
    type: Boolean,
    required: false,
    default: false
  },
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const { loading, get } = useRequest()
const dictData = ref([])
const totalPages = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const selectedValue = ref(props.modelValue)
const isComposing = ref(false)

watch(
  () => props.params,
  async () => {
    //console.log('params changed:', props.params)
    currentPage.value = 1
    // 先获取新数据
    await fetchDictData()

    // 检查当前选中的值是否在新数据中存在
    // const valueExists = dictData.value.some((item) => item.code === selectedValue.value)
    // if (!valueExists) {
    //   // 如果值不存在于新数据中，才清空选择
    //   selectedValue.value = ''
    //   emit('update:modelValue', '')
    // }
  },
  { deep: true }
)

watch(selectedValue, (newValue) => {
  //console.log('selectedValue changed:', newValue)
  emit('update:modelValue', newValue)
  if (newValue) {
    translateDictData()
  }
})

// 监听modelValue的变化,设值的时候，进行翻译
watch(
  () => props.modelValue,
  (newValue) => {
    //console.log('modelValue changed:', newValue)
    if (selectedValue.value !== newValue) {
      selectedValue.value = newValue
      if (newValue) {
        translateDictData()
      }
    }
  }
)

const fetchDictData = async () => {
  try {
    let _dictData, _totalPages

    const commonParams = {
      searchField: 'detail',
      page: currentPage.value,
      pageSize: pageSize.value
    }

    if (props.type === 'static') {
      const response = await get('/StaticDict/load', {
        kind: props.kind,
        ...commonParams
      })
      _dictData = response.dictItems ?? []
      _totalPages = Math.ceil(response.totalCount / pageSize.value ?? 0)
    } else if (props.type === 'dynamic') {
      //console.log('fetching dynamic dict:', props.params)
      const response = await get('/DynamicDict/load', {
        ...props.params,
        ...commonParams
      })
      //console.log('fetch response:', response)
      _dictData = response.dictItems ?? []
      _totalPages = Math.ceil(response.totalCount / pageSize.value ?? 0)
    }
    dictData.value = _dictData
    totalPages.value = _totalPages
    searchQuery.value = null
  } catch (error) {
    //console.error('Error fetching dict data:', error)
  }
}

const translateDictData = async () => {
  if (!selectedValue.value) return

  if (props.type === 'static') {
    const response = await get('/StaticDict/loadItem', {
      kind: props.kind,
      searchField: 'detail',
      code: selectedValue.value
    })
    dictData.value = response ?? []
    totalPages.value = Math.ceil(response?.length / pageSize.value ?? 0)
  } else if (props.type === 'dynamic') {
    try {
      //console.log('translating dynamic dict:', props.params, selectedValue.value)
      const response = await get('/DynamicDict/loadItem', {
        ...props.params,
        searchField: 'detail',
        code: selectedValue.value
      })
      //console.log('translate response:', response)
      if (response) {
        dictData.value = Array.isArray(response) ? response : [response]
      }
    } catch (error) {
      //console.error('Error translating dict data:', error)
    }
  }
}

const searchDictData = async () => {
  const commonParams = {
    searchField: 'code;detail;spell',
    page: currentPage.value,
    pageSize: pageSize.value,
    query: searchQuery.value
  }

  if (props.type === 'static') {
    const response = await get('/StaticDict/search', {
      kind: props.kind,
      ...commonParams
    })

    dictData.value = response?.dictItems ?? []
    totalPages.value = Math.ceil(response?.totalCount / pageSize.value ?? 0)
  } else if (props.type === 'dynamic') {
    const response = await get('/DynamicDict/search', {
      ...props.params,
      ...commonParams
    })
    dictData.value = response?.dictItems ?? []
    totalPages.value = Math.ceil(response?.totalCount / pageSize.value ?? 0)
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  searchQuery.value ? searchDictData() : fetchDictData()
}

// 创建防抖后的搜索方法
const debouncedSearchDictData = debounce(async () => {
  await searchDictData()
}, 300)

// 处理输入法开始
const handleCompositionStart = () => {
  isComposing.value = true
}

// 处理输入法结束
const handleCompositionEnd = () => {
  isComposing.value = false
}

// 修改远程搜索方法
const remoteSearch = (query) => {
  if (isComposing.value) {
    return
  }

  if (query) {
    searchQuery.value = query
    currentPage.value = 1
    debouncedSearchDictData()
  } else if (dictData.value?.length <= 1) {
    fetchDictData()
  }
}

if (props.modelValue) {
  translateDictData()
}
</script>
