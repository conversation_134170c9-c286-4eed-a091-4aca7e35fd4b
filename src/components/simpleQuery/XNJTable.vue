<template>
  <div class="xnj-table">
    <el-table
      header-row-class-name="table-header"
      :data="dataSource"
      :border="true"
      :stripe="true"
      style="width: 100%"
      fit
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column
        v-for="(item, index) in props.columns"
        :key="index"
        :prop="item.colName"
        :label="item.cname"
        :width="item.width"
      >
        <template #default="scope">
          <template v-if="item.slot">
            <slot :row="scope.row" :name="item.slot"></slot>
          </template>
          <template v-else>
            {{ scope.row[item.colName] }}
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'


const props = defineProps({
  dataSource: {
    type: Array,
    default: () => {
      return []
    }
  },
  columns: {
    type: Array,
    default: () => {
      return []
    }
  }
})

const dataSource = ref(props.dataSource)

watch(() => props.dataSource, (newVal, oldVal) => {
  console.log('newVal,oldVal', newVal, oldVal)
  // 监听数据变化，更新dataSource
  dataSource.value = newVal
})
</script>
<style lang="less" scoped>
.xnj-table {
  width: 100%;
  margin-bottom: 12px;
}

:deep(.table-header th) {
  background-color: #f5f7fa;
  color: #212121;
}
</style>
