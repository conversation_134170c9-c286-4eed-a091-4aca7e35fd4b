<template>
  <el-form :inline="true" :model="formInline" class="demo-form-inline">
    <el-form-item label="Approved by">
      <el-input v-model="formInline.user" placeholder="Approved by" clearable />
    </el-form-item>
    <el-form-item label="时间范围">
      <el-date-picker
        v-model="formInline.time"
        type="daterange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>
    <el-form-item label="Activity time">
      <el-date-picker v-model="formInline.date" type="date" placeholder="Pick a date" clearable />
    </el-form-item>
    <el-form-item label="字典">
      <DictSelect v-model="formInline.dict" :showCode="false" type="static" kind="06" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="onSubmit">Query</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive, emit } from 'vue'

const formInline = reactive({
  user: '',
  region: '',
  date: '',
  time: [],
  dict: ''
})

const onSubmit = () => {
  console.log('submit!')
  emit('onSubmit', formInline)
}
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
</style>
