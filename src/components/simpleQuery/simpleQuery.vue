<template>
  <QueryCondition
    v-if="props.queryConditions?.length"
    :fields="props.queryConditions"
    @onSubmit="handleSubmit"
  ></QueryCondition>

  <div v-if="props.operations?.length" class="btn-area">
    <el-button
      v-for="(item, index) in props.operations"
      :key="index"
      :type="item.type || 'primary'"
      @click="item.handle($attrs)"
    >
      {{ item.name }}
    </el-button>
  </div>

  <XNJTable :dataSource="tableData" :columns="tableColumns" v-loading="loading">
    <template v-for="(index, name) in $slots" v-slot:[name]="data">
      <slot :name="name" v-bind="data"></slot>
    </template>
  </XNJTable>
  <div class="pagination-container">
    <div class="left-buttons">
      <el-button type="primary" :icon="Refresh" circle @click="queryTableData()" />
      <el-button
        type="primary"
        :icon="Download"
        circle
        @click="handleExportExcel"
        :loading="exportLoading"
      />
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pageSize"
      small
      background
      layout="->,total, sizes, prev, pager, next, jumper"
      :total="totalItems"
    />
  </div>
</template>

<script setup>
import { reactive, onMounted, ref, defineExpose } from 'vue'
import { useRequest } from '@/utils/request/useRequest'
import { Refresh, Download } from '@element-plus/icons-vue'
import QueryCondition from './queryCondition.vue'
import XNJTable from './XNJTable.vue'
const exportLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0) // Replace this with your total data count
const loading = ref(false)
const tableData = ref([])
const { get } = useRequest()
const props = defineProps({
  queryConditions: {
    type: Array,
    default: () => []
  },
  tableColumns: {
    type: Array,
    default: () => []
  },
  queryId: {
    type: String,
    default: ''
  },
  operations: {
    type: Array,
    default: () => []
  },
  queryParams: {
    type: Object,
    default: () => {}
  }
})

const queryTableData = async (page = 1, size = 10, queryParams = {}) => {
  loading.value = true
  const params = Object.keys(queryParams).length ? queryParams : props.queryParams
  const res = await get(`/simplequery/${props.queryId}`, {
    page,
    pageSize: size,
    ...params
  })
  const result = res.resultList
  const tdata = convertTableData(result)
  tableData.value = tdata
  console.log('tableData', tableData.value)
  totalItems.value = res.totalRecord
  loading.value = false
}

onMounted(async () => {
  console.info('onMounted', props.queryParams)
  await queryTableData()
})

const convertTableData = (data) => {
  const rawData = data.map((item) => {
    const row = {}
    item.forEach((item) => {
      row[item.name] = item.value
    })
    return row
  })
  return rawData
}

const handleSubmit = (data) => {
  console.log('submit', data)
  const updatedQueryParams = { ...props.queryParams, ...data }
  queryTableData(currentPage.value, pageSize.value, updatedQueryParams)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  queryTableData(currentPage.value, pageSize.value)
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  queryTableData(currentPage.value, pageSize.value)
}

const handleExportExcel = async () => {
  exportLoading.value = true
  try {
    const res = await get(
      `/simplequery/excel/${props.queryId}`,
      {
        ...props.queryParams
      },
      {
        responseType: 'blob'
      }
    )

    // 创建下载链接
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `导出数据_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('导出失败:', error)
  } finally {
    exportLoading.value = false
  }
}

defineExpose({
  queryTableData
})
</script>

<style lang="less" scoped>
.btn-area {
  margin-bottom: 10px;
  margin-top: 24px;
}
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;

  .left-buttons {
    display: flex;
    gap: 8px;
  }
}
</style>
