<template>
  <div class="query-fields">
    <el-form ref="ruleFormRef" :model="queryForm" label-width="auto">
      <el-row :gutter="20">
        <el-col :span="8" v-for="field in visibleFields" :key="field.colName">
          <el-form-item :label="field.cname">
            <component
              :is="getFieldComponent(field.inputType)"
              v-model="queryForm[field.colName]"
              v-bind="getFieldProps(field)"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="8"
          :offset="visibleFields?.length % 3 === 0 ? 16 : visibleFields?.length % 3 === 2 ? 0 : 8"
        >
          <el-form-item>
            <div class="button-group">
              <a href="javascript:;" class="link-button" @click.prevent="toggleFormVisibility">
                {{ isFormExpanded ? '收起' : '展开' }}
              </a>
              <el-select v-model="queryType" class="query-type-select">
                <el-option label="精确查询" value="exact" />
                <el-option label="模糊查询" value="fuzzy" />
              </el-select>
              <el-button type="primary" @click="onSubmit(ruleFormRef)">查询</el-button>
              <el-button @click="resetForm(ruleFormRef)">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, toRaw, computed } from 'vue'

import DictSelect from '../dict/DictSelect.vue'

const emit = defineEmits(['onSubmit'])
// 假设你有一个控制表单展开/收起的状态
const isFormExpanded = ref(true)
const ruleFormRef = ref()
const maxVisibleFields = 3
const queryType = ref('exact') // 默认精确查询
const props = defineProps({
  fields: {
    type: Array,
    default: () => []
  }
})

const queryForm = reactive({})
const visibleFields = computed(() => {
  // 当展开时显示所有字段，收起时只显示前3个
  return props.fields.slice(0, isFormExpanded.value ? props.fields.length : maxVisibleFields)
})
// 定义切换表单可见性的方法
const toggleFormVisibility = () => {
  isFormExpanded.value = !isFormExpanded.value

  if (isFormExpanded.value) {
    const initialFormData = props.fields.slice(0, maxVisibleFields).reduce((acc, cur) => {
      return { ...acc, [cur.colName]: '' }
    }, {})
    const newqueryForm = reactive({ ...initialFormData })
    Object.assign(queryForm, newqueryForm)
  } else {
    const allFormData = props.fields.reduce((acc, cur) => {
      return { ...acc, [cur.colName]: '' }
    }, {})
    const newqueryForm = reactive({ ...allFormData })
    Object.assign(queryForm, newqueryForm)
  }
}

const getFieldComponent = (type) => {
  switch (type) {
    case 'date':
      // eslint-disable-next-line no-undef
      return ElDatePicker
    case 'dateRange':
      // eslint-disable-next-line no-undef
      return ElDatePicker
    case 'dict':
      return DictSelect
    default:
      // eslint-disable-next-line no-undef
      return ElInput
  }
}

const getFieldProps = (field) => {
  const commonProps = {
    placeholder: `请输入${field.cname}`
  }
  switch (field.inputType) {
    case 'date':
      return {
        placeholder: `请选择${field.cname}`,
        format: 'YYYY-MM-DD',
        'value-format': 'YYYY-MM-DD'
      }
    case 'dateRange':
      return {
        format: 'YYYY-MM-DD',
        type: 'daterange',
        'range-separator': '-',
        'value-format': 'YYYY-MM-DD',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间'
      }
    case 'dict':
      if (field.type === 'static') {
        return {
          type: field.type,
          kind: field.kind,
          showcode: field.showcode,
          placeholder: `请选择${field.cname}`
        }
      } else if (field.type === 'dynamic') {
        return {
          type: field.type,
          params: field.params,
          placeholder: `请选择${field.cname}`
        }
      }
      return {}
    default:
      return commonProps
  }
}

// 判断字段是否可以进行模糊查询
const canBeFuzzy = (field) => {
  return (
    field.inputType === 'string' || (field.inputType !== 'dateRange' && field.inputType !== 'date')
  )
}

const onSubmit = async (formRef) => {
  await formRef.validate((valid, fields) => {
    if (valid) {
      const data = {}
      const rawForm = toRaw(queryForm)

      for (const key in rawForm) {
        let value = rawForm[key]
        const field = props.fields.find((f) => f.colName === key)

        if (!field) continue

        // 处理日期范围
        if (field.inputType === 'dateRange' && Array.isArray(value)) {
          if (value[0]) {
            data[`queryCondition_${key}_start`] = value[0]
          }
          if (value[1]) {
            data[`queryCondition_${key}_end`] = value[1]
          }
          continue
        }

        // 处理其他类型
        if (value && field && canBeFuzzy(field) && queryType.value === 'fuzzy') {
          // 如果值存在且以"00"结尾，则只取前4位
          if (typeof value === 'string' && value.endsWith('00')) {
            value = value.slice(0, 4)
          }
          data[`queryCondition_${key}`] = `${value}%`
        } else {
          data[`queryCondition_${key}`] = value
        }
      }

      // 设置全局查询模式
      if (queryType.value === 'fuzzy') {
        data['GLOBAL_STRING_MATCH_MODE'] = 'fuzzy'
      } else {
        data['GLOBAL_STRING_MATCH_MODE'] = 'exact'
      }

      emit('onSubmit', data)
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formRef) => {
  if (!formRef) return
  formRef.resetFields()
}
</script>

<style scoped>
.query-fields {
  width: 100%;
}
.botton-col {
  display: flex;
  justify-content: flex-end;
}
.button-group {
  display: flex;
  width: 100%;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
}
.link-button {
  color: #409eff; /* 设置为类似主按钮的颜色 */
  cursor: pointer;
  text-decoration: underline;
  margin-left: 10px;
  margin-right: 10px;
}

.query-type-select {
  width: 120px;
}
</style>
