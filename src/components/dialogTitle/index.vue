<template>
  <div class="my-header">
    <span :id="titleId">{{ title }}</span>
    <span class="close" @click="close">
      <el-icon><Close /></el-icon
    ></span>
  </div>
</template>

<script setup>
const emit = defineEmits(['close'])
defineProps({
  titleId: {
    type: String,
    default: ''
  },
  titleClass: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  }
})

const close = () => {
  emit('close')
}
</script>

<style lang="less" scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: @themeColor;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  color: #fff;
  padding: 0 6px 0 12px;
}
.close {
  :hover {
    cursor: pointer;
    color: @themeSecondColor;
  }
}
</style>
