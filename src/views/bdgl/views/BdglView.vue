<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import { ref } from 'vue'

// 模态框相关数据
const dialogVisible = ref(false)
const currentRowData = ref({})

const handleAdd = () => {
  window.open(`/#/bdgl?type=add`, '_blank')
}

const handleEdit = (row) => {
  currentRowData.value = row
  dialogVisible.value = true
}

const queryConditions = [
  {
    cname: '人员编号',
    colName: 'RYBH',
    inputType: 'string'
  },
  {
    cname: '人员类型',
    colName: 'RYLX',
    inputType: 'dict',
    type: 'static',
    kind: 'rylx'
  },
  {
    cname: '姓名',
    colName: 'XM',
    inputType: 'string'
  },
  {
    cname: '证件号码',
    colName: 'ZJHM',
    inputType: 'string'
  },
  {
    cname: '行政区划',
    colName: 'XZQH',
    inputType: 'dict',
    type: 'static',
    kind: '06'
  },
  {
    cname: '主办单位',
    colName: 'ZBDW',
    inputType: 'dict',
    type: 'static',
    kind: '06'
  },
  {
    cname: '对比类型',
    colName: 'DBLX',
    inputType: 'dict',
    type: 'static',
    kind: 'dblx'
  },
  {
    cname: '对比结果',
    colName: 'DBJG',
    inputType: 'dict',
    type: 'static',
    kind: 'dbjg'
  },
  {
    cname: '对比时间',
    colName: 'DBSJ',
    inputType: 'dateRange'
  }
]

const queryId = 'V_SWXXBD_ZWHMXX20250707164800'

const tableColumns = [
  {
    colName: 'RYBH',
    cname: '人员编号',
    slot: 'BH'
  },
  {
    colName: 'RYLX',
    cname: '人员类型'
  },
  {
    colName: 'XM',
    cname: '姓名'
  },
  {
    colName: 'XB',
    cname: '性别'
  },
  {
    colName: 'ZJHM',
    cname: '证件号码'
  },
  {
    colName: 'XZQH',
    cname: '行政区划'
  },
  {
    colName: 'HJDZ',
    cname: '户籍地址'
  },
  {
    colName: 'XZZ',
    cname: '现住址'
  },
  {
    colName: 'ZBDW',
    cname: '主办单位'
  },
  {
    colName: 'ZBMJ',
    cname: '主办民警'
  },
  {
    colName: 'DBLX',
    cname: '对比类型'
  },
  {
    colName: 'DBJG',
    cname: '对比结果'
  },
  {
    colName: 'DBSJ',
    cname: '对比时间'
  }
]

const operations = [
  {
    name: '添加',
    type: 'primary',
    handle: handleAdd
  }
]
</script>

<template>
  <HeaderTitle title="比对管理查询"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #BH="prop">
          <a href="javascript:void(0)" @click="handleEdit(prop.row)">{{ prop.row.RYBH }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>

  <!-- 详情模态框 -->
  <el-dialog v-model="dialogVisible" :show-close="false" width="80%">
    <template #header="{ close, titleId }">
      <DialogTitle title="比对管理详情" @close="close" :id="titleId" />
    </template>
    <div v-if="currentRowData" class="detail-content">
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">人员编号:</span>
            <span class="detail-value">{{ currentRowData.RYBH || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">人员类型:</span>
            <span class="detail-value">{{ currentRowData.RYLX || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">姓名:</span>
            <span class="detail-value">{{ currentRowData.XM || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">性别:</span>
            <span class="detail-value">{{ currentRowData.XB || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">证件号码:</span>
            <span class="detail-value">{{ currentRowData.ZJHM || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">行政区划:</span>
            <span class="detail-value">{{ currentRowData.XZQH || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">户籍地址:</span>
            <span class="detail-value">{{ currentRowData.HJDZ || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">现住址:</span>
            <span class="detail-value">{{ currentRowData.XZZ || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">主办单位:</span>
            <span class="detail-value">{{ currentRowData.ZBDW || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">主办民警:</span>
            <span class="detail-value">{{ currentRowData.ZBMJ || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">对比类型:</span>
            <span class="detail-value">{{ currentRowData.DBLX || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">对比结果:</span>
            <span class="detail-value">{{ currentRowData.DBJG || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">对比时间:</span>
            <span class="detail-value">{{ currentRowData.DBSJ || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}

/* 模态框样式 */
:deep(.el-dialog) {
  --el-dialog-padding-primary: 0;
}

.detail-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.detail-label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-value {
  color: #303133;
  flex: 1;
}
</style>
