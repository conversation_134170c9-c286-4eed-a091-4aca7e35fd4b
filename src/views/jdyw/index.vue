<template>
  <div class="main-container">
    <HeaderTitle :title="title"></HeaderTitle>
    <div class="screenLayout">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleTabClick">
        <el-tab-pane lazy label="发送业务" name="fsyw">
          <FsywSimpleQuery></FsywSimpleQuery>
        </el-tab-pane>
        <el-tab-pane lazy label="接收业务" name="jsyw">
          <jsyw-simple-query></jsyw-simple-query>
        </el-tab-pane>
        <el-tab-pane lazy label="文书呈请" name="wscq"> <WscqSimpleQuery/> </el-tab-pane>
        <el-tab-pane lazy label="评估意见" name="pgyj">
          <PgyjSimpleQuery></PgyjSimpleQuery>
        </el-tab-pane>
        <el-tab-pane lazy label="出所信息" name="csxx">
          <CsxxSimpleQuery></CsxxSimpleQuery>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HeaderTitle from '@/components/headerTitle/index.vue'
import FsywSimpleQuery from './views/FsywSimpleQuery.vue'
import JsywSimpleQuery from '@/views/jdyw/views/JsywSimpleQuery.vue'
import PgyjSimpleQuery from '@/views/jdyw/views/PgyjSimpleQuery.vue'
import CsxxSimpleQuery from '@/views/jdyw/views/CsxxSimpleQuery.vue'
import WscqSimpleQuery from '@/views/jdyw/views/WscqSimpleQuery.vue'

const activeName = ref('fsyw')
const title = '戒毒业务'

const handleTabClick = (tab, event) => {
  console.log(tab, event)
}
</script>

<style lang="less" scoped>
.main-container {
  height: 100%;
  width: 100%;
}
</style>
