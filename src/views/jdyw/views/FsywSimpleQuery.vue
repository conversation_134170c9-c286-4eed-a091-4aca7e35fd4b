<template>
  <div class="container">
    <SimpleQuery
      ref="queryTableRef"
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button v-if="prop.row.SENDTIME" size="small" @click="handleView(prop.row)"
          >查看</el-button
        >
        <el-button v-if="!prop.row.SENDTIME" size="small" @click="handleEdit(prop.row)"
          >编辑</el-button
        >
        <el-popconfirm
          v-if="!prop.row.SENDTIME"
          title="确定删除吗?"
          @confirm="handleDelete(prop.row)"
        >
          <template #reference>
            <el-button size="small" type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
      <!--      <template #AJBH="prop">-->
      <!--        <a href="javascript:void(0)" @click="handleEdit(prop.row)">{{ prop.row.AJBH }}</a>-->
      <!--      </template>-->
    </SimpleQuery>
    <el-dialog destroy-on-close v-model="jdywStore.addDialogVisble" :show-close="false" width="80%">
      <template #header="{ close, titleId }">
        <DialogTitle title="添加" @close="close" :id="titleId" />
      </template>
      <FsywAddForm />
    </el-dialog>
  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import FsywAddForm from './fsywAddForm.vue'
import { ref, watch } from 'vue'
import UseConfig from '../configs/query-V_B_JZJD_BUSINESS_JB202403211648'
import { useJdywStore } from '../stores/jdywfs'
import { getUrlParams } from '@/utils/common.js'
// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
const jdywStore = useJdywStore()
const queryTableRef = ref(null)
const params = getUrlParams()

const visble = ref(false)

watch(
  () => jdywStore.addDialogVisble,
  (newVal) => {
    if (!newVal) {
      //当关闭弹窗时，刷新表格
      queryTableRef.value.queryTableData()
    }
  }
)

const handleEdit = async (row) => {
  console.log(row)
  await jdywStore.getRowData(row.SYSTEMID)
  jdywStore.addDialogVisble = true
  jdywStore.mode = 'edit'
}

const handleView = async (row) => {
  console.log(row)
  await jdywStore.getRowData(row.SYSTEMID)
  jdywStore.addDialogVisble = true
  jdywStore.mode = 'view'
}

const handleDelete = async (row) => {
  await jdywStore.deleteRowData(row.SYSTEMID)
  console.log('queryTableRef.value', queryTableRef.value)
  await queryTableRef.value.queryTableData()
}
</script>

<style lang="less" scoped>
.container {
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
