<template>
  <div class="container">
    <el-form label-width="auto" :model="formData" style="max-width: 600px; margin: auto">
      <el-form-item label="处理人:">
        <el-input v-model="formData.clr" />
      </el-form-item>
      <el-form-item label="呈请人:">
        <el-input v-model="formData.cqr" />
      </el-form-item>
      <el-form-item label="呈请时间:">
        <el-date-picker
          style="width: 100%"
          v-model="formData.cqsj"
          type="datetime"
          placeholder="请选择呈请时间"
        />
      </el-form-item>
      <el-form-item label="下一环节审批">
        <span>{{ nextTask }}</span>
      </el-form-item>
      <el-form-item label="下一环节接收人:">
        <el-checkbox-group v-model="formData.jsr">
          <el-checkbox
            v-for="item in nextTaskAssignments"
            :key="item.userid"
            :label="item.username"
            :value="item.userid"
          />
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div class="btn-area">
      <el-button type="text" @click="$emit('submit')">重置</el-button>
      <el-button type="primary" @click="handleCQSP">提交</el-button>
    </div>
  </div>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue'
import { useSpxxStore } from '@/views/jdyw/stores/spxx.js'
import szzsUtil from 'jit-auth'
import { ElMessage } from 'element-plus'
import router from '@/router/index.js'
import { getNormalUrlParams } from '@/utils/common.js'

const spxxStore = useSpxxStore()
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
const formData = reactive({
  clr: userInfo.loginUserDTO.name,
  cqr: userInfo.loginUserDTO.name,
  cqsj: '2024-04-15 10:44:32',
  jsr: []
})
const nextTaskAssignments = ref([])
const nextTask = ref('')
const workItemId = ref('')
const params = getNormalUrlParams()
onMounted(async () => {
  const res = await spxxStore.getNextUsers(params.wsid)
  nextTaskAssignments.value = res?.data?.nextTaskAssignments
  nextTask.value = res?.data?.nextTask
  const res2 = await spxxStore.getWorkItemId(params.wsid)
  workItemId.value = res2?.data?.WORKITEMID
})

const options = {
  clientUrl: window.config.pnxClientPath,
  keyUrl: '/api/jitgw/jitGWRandom', // 获取签名随机值接口地址
  signUrl: '/api/jitLogin', // 证书调用成功登录地址
  initParam: `<?xml version="1.0" encoding="utf-8"?><authinfo><liblist><lib type="CSP" version="1.0" dllname="" ><algid val="SHA1" sm2_hashalg="sm3"/></lib><lib type="SKF" version="1.1" dllname="SERfR01DQUlTLmRsbA==" ><algid val="SHA1" sm2_hashalg="sm3"/></lib><lib type="SKF" version="1.1" dllname="U2h1dHRsZUNzcDExXzMwMDBHTS5kbGw=" ><algid val="SHA1" sm2_hashalg="sm3"/></lib><lib type="SKF" version="1.1" dllname="U0tGQVBJLmRsbA==" ><algid val="SHA1" sm2_hashalg="sm3"/></lib></liblist></authinfo>`, // 签名初始化参数
  successCall: (url, data) => {
    console.log('data', data)
    // 获取签名成功
    // const { serialNumber, sign } = data;
  }
}

const handleCQSP = async () => {
  console.log('formData', formData)
  // szzsUtil.loginByPNX(options);
  const postData = {
    wsId: params.wsid,
    workItemId: workItemId.value,
    clsj: formData.cqsj,
    spyj: '拟同意',
    spjg: '01',
    nextTaskAssignments: formData.jsr
  }
  const spReult = await spxxStore.postToNextSP(postData)
  if (spReult.status === 200) {
    ElMessage.success({
      message: '审批成功.',
      onClose: () => {
        router.back()
      }
    })
  }
}
</script>
<style scoped lang="less">
.container {
  width: 532px;
  padding: 48px 64px 0px;
  box-sizing: border-box;
}

.btn-area {
  padding: 24px;
  text-align: right;
}
</style>
