<template>
  <div class="container" v-loading="loading">
    <el-descriptions title="详细信息" border :column="3" size="default">
      <template #extra>
        <el-button type="primary" @click="handleViewCL">材料信息</el-button>
      </template>
      <el-descriptions-item label="案件编号">{{ formData.ajbh }}</el-descriptions-item>
      <el-descriptions-item label="意见书文号">{{ formData.yjszh }}</el-descriptions-item>
      <el-descriptions-item label="原强制隔离戒毒文书号"></el-descriptions-item>
      <el-descriptions-item label="案件名称">{{ formData.ajmc }}</el-descriptions-item>
      <el-descriptions-item label="建议提前解除期限">{{ formData.jytqjcsj }}</el-descriptions-item>
      <el-descriptions-item label="强制戒毒开始时间">{{ formData.kssj }}</el-descriptions-item>
      <el-descriptions-item label="人员编号">{{ formData.rybh }}</el-descriptions-item>
      <el-descriptions-item label="提前除理由">

        <el-popover
          title="提前除理由"
          :width="800"
          trigger="hover"
          :content="formData.reson"
        >
          <template #reference>
             <span>{{ formData.reson?.length > 14 ? `${formData.reson.substring(0, 14)}...` : formData.reson
               }}</span>
          </template>
        </el-popover>


      </el-descriptions-item>
      <el-descriptions-item label="强制戒毒结束时间">{{ formData.jssj }}</el-descriptions-item>
      <el-descriptions-item label="姓名">{{ formData.xm }}</el-descriptions-item>
      <el-descriptions-item label="戒毒所审批意见">{{ formData.jdsyj }}</el-descriptions-item>
      <el-descriptions-item label="戒毒所意见审批人">{{ formData.jdsyjspr }}</el-descriptions-item>
      <el-descriptions-item label="戒毒所意见审批时间">{{ formData.jdsyjspsj }}</el-descriptions-item>
      <el-descriptions-item label="戒毒决定机关审批意见">{{ formData.jdjdjgspyj }}</el-descriptions-item>
      <el-descriptions-item label="戒毒决定机关审批意见审批人">{{ formData.jdjdjgspyjspr }}</el-descriptions-item>
      <el-descriptions-item label="戒毒决定机关审批时间">{{ formData.jdjdjgspyjspsj }}</el-descriptions-item>
      <el-descriptions-item label="人员在所状态">{{ formData.ryzszt }}</el-descriptions-item>
      <el-descriptions-item label="备注">{{ formData.bz }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>


import { onMounted, ref } from 'vue'
import { useJdywStore } from '@/views/jdyw/stores/jdywjs.js'

const loading = ref(false)
const formData = ref({})
const jdywStore = useJdywStore()
const props = defineProps({
  formId: {
    type: String,
    required: true

  }
})
const handleViewCL = () => {
  jdywStore.jslcVisible = true
}

onMounted(async () => {
    loading.value = true
    const res = await jdywStore.getViewFormData({ systemid: props.formId })
    formData.value = res
    loading.value = false
  }
)


</script>

<style scoped lang="less">
.container {
  width: 80%;
  padding: 20px;
  margin: auto;

}

:deep(.el-popper.is-light)  {
  width: 800px;
}
</style>
