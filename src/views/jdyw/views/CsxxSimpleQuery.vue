<template>
  <div class="container">
    <SimpleQuery
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button size="small" type="primary" @click="view(prop.row)">查看</el-button>
      </template>
    </SimpleQuery>
    <el-dialog v-model="csxxStore.pgyjVisible" :show-close="false" width="80%">
      <template #header="{ close, titleId }">
        <DialogTitle title="详情" @close="close" :id="titleId" />
      </template>
      <!--      <FsywAddForm />-->
      <CSXXViewForm :formId="csxxStore.viewJSId" />
    </el-dialog>
  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import UseConfig from '../configs/query-B_ASJ_JZJD_LSRY202403291600'
import { useCsxxStore } from '../stores/csxx.js'
import { getUrlParams } from '@/utils/common.js'
import CSXXViewForm from '@/views/jdyw/views/CSXXViewForm.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'

const params = getUrlParams()

// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
const csxxStore = useCsxxStore()
const view = (row) => {
  console.log(row)
  csxxStore.viewJSId = row.SYSTEMID
  csxxStore.pgyjVisible = true
}
</script>

<style lang="less" scoped>
.container {
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
