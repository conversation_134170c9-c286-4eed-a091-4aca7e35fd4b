<template>
  <HeaderTitle title="审批信息"></HeaderTitle>
  <div class="container">
    <SimpleQuery
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button size="small" type="primary" @click="view(prop.row,'sp')">审批</el-button>
      </template>
    </SimpleQuery>
    <!--    <el-dialog v-model="csxxStore.dialogVisible" :show-close="false" width="80%">-->
    <!--      <template #header="{ close, titleId }">-->
    <!--        <DialogTitle title="详情" @close="close" :id="titleId" />-->
    <!--      </template>-->
    <!--&lt;!&ndash;      <FsywAddForm />&ndash;&gt;-->
    <!--      <PGYJViewForm :formId="csxxStore.viewJSId" />-->
    <!--    </el-dialog>-->
  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import UseConfig from '../configs/query-V_ASJ_JZJD_YJWSSP202403291600'
import { useSpxxStore } from '../stores/spxx.js'
import { getUrlParams } from '@/utils/common.js'
import PGYJViewForm from '@/views/jdyw/views/PGYJViewForm.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'
import router from '@/router/index.js'

const params = getUrlParams()

// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
// const  csxxStore = useSpxxStore()
const view = (row, type) => {
  console.log(row)
  // csxxStore.viewJSId = row.SYSTEMID;
  // csxxStore.dialogVisible = true;
  window.open(`/#/approval?wsid=${row.SYSTEMID}&type=${type}`, '_blank')
  // router.push({
  //   name: 'approval',
  //   query: {
  //     wsid: row.SYSTEMID,
  //     type: type
  //   }
  // })
}
</script>

<style lang="less" scoped>
.container {
  padding: 22px;

  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
