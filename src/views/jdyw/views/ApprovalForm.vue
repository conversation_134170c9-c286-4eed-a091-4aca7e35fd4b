<template>
  <div class="container" v-loading="loading">
    <HeaderTitle title="文书审批"></HeaderTitle>
    <div class="content">
      <div class="pdf-container">
        <PDF
          :src="pdfStream"
          :cMapUrl="cmapsPath"
          style="width: 680px;max-height: 1080px"
          @onComplete="handlePDFloaded"
        />
      </div>
      <div class="cq-area">
        <table>
          <tr>
            <td class="tb-title">呈请单位</td>
            <td class="tb-content" colspan="3">
              <DictSelect
                v-model="formData.cqdw"
                :showCode="false"
                type="static"
                kind="06"
              />
            </td>
          </tr>
          <tr>
            <td class="tb-title">呈请人</td>
            <td class="tb-content">
              <DictSelect
                v-model="formData.cqr"
                :showCode="false"
                type="dynamic"
                :params="{ configId: 'jzjd_fs_ywlx' }"
              />
            </td>
            <td class="tb-title">呈请时间</td>
            <td>
              <el-date-picker
                v-model="formData.cqsj"
                type="datetime"
                placeholder="请选择呈请时间"
              />
            </td>
          </tr>
        </table>
      </div>
      <div class="btn-area">
        <!--        呈请-->
        <el-button v-if="type !== 'ck'" type="primary" @click="handleClickCQ">{{ type === 'cq' ? '呈请' : '审批' }}</el-button>
        <!--        审批-->
        <!--        <el-button type="primary" @click="$emit('approve')">同意</el-button>-->
        <el-button type="primary" @click="$emit('close')">关闭</el-button>
      </div>
    </div>
    <el-dialog v-model="spxxStore.dialogVisible" :show-close="false" width="600px">
      <template #header="{ close, titleId }">
        <DialogTitle title="审批" @close="close" :id="titleId" />
      </template>
      <CpFormView />
    </el-dialog>
  </div>

</template>
<script setup>
import PDF from 'pdf-vue3'
import HeaderTitle from '@/components/headerTitle/index.vue'
import DictSelect from '@/components/dict/DictSelect.vue'
import { onMounted, reactive, ref } from 'vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import { useSpxxStore } from '@/views/jdyw/stores/spxx.js'
import CpFormView from '@/views/jdyw/views/CpFormView.vue'
import { getNormalUrlParams } from '@/utils/common.js'

const spxxStore = useSpxxStore()
const cmapsPath = window.config.cmapsPath
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
const params = getNormalUrlParams()
const loading = ref(true);
const type = params.type;

const formData = reactive({
  cqsj: '2024-04-15 10:27:17',
  cqr: userInfo.loginUserDTO.jh,
  cqdw: userInfo.loginUserDTO.dept
})
const pdfStream = ref('')

const handleClickCQ = () => {
  spxxStore.dialogVisible = true
}

const handlePDFloaded = ()=>{
  loading.value = false;
}

onMounted(async () => {

  console.log('params', params)
  const pdfblob = await spxxStore.getWsPDF(params.wsid)
  blobToDataURI(pdfblob)

})

// blob 转 base64
const blobToDataURI = (blob) => {
  const reader = new FileReader()
  reader.readAsDataURL(blob)
  reader.onload = function(e) {
    pdfStream.value = e.target.result
    // callback(e.target.result);
  }
}

</script>


<style scoped lang="less">
.container {
  width: 100%;
  height: 100%;
  //overflow: hidden;
}

.container {
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}

.cq-area {
  width: 680px;
  margin: auto;
}

.content {
  height: calc(100vh - 40px);
  overflow: auto;
  padding-top: 20px;
}

.pdf-container {
  width: 680px;
  margin: auto;
  border: 1px solid #ccc;
}


table {
  border-collapse: collapse;
  border: 1px solid #ccc;
  width: 100%; /* 使表格占据父容器的宽度 */
}

th, td {
  border: 1px solid #ccc;
  padding: 8px;
}

.tb-title {
  width: 15%;
  text-align: center;
}

.tb-content {
  width: 35%;
}

.btn-area {
  width: 680px;
  text-align: center;
  margin: 20px auto 68px;
}

:deep(.pdf-vue3-scroller) {
  max-height: 1080px !important;
}

:deep(.el-select__wrapper) {
  box-shadow: none !important;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focused) {
  box-shadow: none !important;
}

:deep(.el-select__wrapper.is-focused) {
  box-shadow: none !important;
}
</style>
