<template>
  <div class="container">
    <SimpleQuery
      :queryId="queryId"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button size="small" type="primary" @click="viewCL(prop.row)">查看</el-button>
      </template>
    </SimpleQuery>
  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import UseConfig from '../configs/query-B_ASJ_JZJD_REFILE202403211755'
import { useRequest } from '@/utils/request/useRequest.js'

const { id: queryId, operation, condition, column } = UseConfig()
const { post } = useRequest()

const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const params = {
  systemid: props.id
}

const viewCL = (row) => {
  console.log(row)
  const postData = {
    bucket: row.BUCKET,
    pathurl: row.PATHURL,
    filename: row.FILENAME
  }

  post('/api/b_asj_jzjd_refiles/downloadFiles', [postData], {
    headers: {
      'Content-Type': 'application/json' // 根据实际情况设置
    },
    responseType: 'blob'
  })
    .then((res) => {
      // 创建zip文件的Blob对象
      const blob = new Blob([res], { type: 'application/zip' })

      // 直接使用原始文件名
      let filename = 'files.zip'

      // 创建一个下载链接
      const downloadLink = document.createElement('a')
      downloadLink.href = window.URL.createObjectURL(blob)
      downloadLink.download = filename // 使用原始文件名
      // 将链接添加到页面中并模拟点击
      document.body.appendChild(downloadLink)
      downloadLink.click()
      // 清理资源
      window.URL.revokeObjectURL(downloadLink.href)
      document.body.removeChild(downloadLink)
    })
    .catch((error) => {
      console.error('下载文件失败:', error)
      // 可以在这里添加错误提示
    })
}
</script>

<style lang="less" scoped>
.container {
  padding: 28px;
  min-height: 280px;
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
