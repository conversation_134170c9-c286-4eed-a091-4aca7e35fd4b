<template>
  <div class="container">
    <SimpleQuery
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button size="small" type="primary" @click="view(prop.row)">查看</el-button>
      </template>

    </SimpleQuery>
    <el-dialog v-model="pgyjStore.pgyjVisible" :show-close="false" width="80%">
      <template #header="{ close, titleId }">
        <DialogTitle title="详情" @close="close" :id="titleId" />
      </template>
<!--      <FsywAddForm />-->
      <PGYJViewForm :formId="pgyjStore.viewJSId" />
    </el-dialog>






  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import UseConfig from '../configs/query-B_ASJ_JZJD_YJWS_PGYJ202403291648'
import { usePGYJStore } from '../stores/pgyj.js'
import { getUrlParams } from '@/utils/common.js'
import PGYJViewForm from '@/views/jdyw/views/PGYJViewForm.vue'


const params = getUrlParams();

// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
const  pgyjStore = usePGYJStore()
const view = (row) => {
  console.log(row)
  pgyjStore.viewJSId = row.SYSTEMID;
  pgyjStore.pgyjVisible = true;
}

</script>

<style lang="less" scoped>
.container {
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
