<template>
  <div class="container" v-loading="loading">
    <!-- -->
    <el-steps :active="activeStep" align-center>
      <el-step title="步骤一" description="选择业务类型、机构类型、名称" />
      <el-step title="步骤二" description="选择对象" />
      <el-step title="步骤三" description="选择相关材料" />
    </el-steps>
    <div class="content">
      <!-- -->
      <div class="title">
        <span class="title-text">{{ title }}</span>
      </div>
      <div class="content-body" v-if="activeStep === 1">
        <!-- <el-form :inline="true" :model="formData">
            <el-input v-model="formData.ajbh" placeholder="案件编号" clearable />
        </el-form> -->
        <el-descriptions class="formBody" :column="3" border>
          <el-descriptions-item class="formItem" label="案件编号">
            <el-input disabled v-model="formData.ajbh" placeholder="案件编号" clearable />
          </el-descriptions-item>
          <el-descriptions-item class="formItem" label="案件名称">
            <el-input disabled v-model="formData.ajmc" placeholder="案件名称" clearable />
          </el-descriptions-item>
          <el-descriptions-item class-name="contentStyle" class="formItem" label="业务类型">
            <DictSelect
              v-model="formData.ywlx"
              :showCode="false"
              type="dynamic"
              :params="{ configId: 'jzjd_fs_ywlx' }"
            />
          </el-descriptions-item>
          <!-- <el-descriptions-item class="formItem" label="机构类型">
            <DictSelect
              v-model="formData.jglx"
              :showCode="false"
              type="dynamic"
              :params="{ configId: 'jzjd_jglx_dynamic', code: formData.ywlx }"
            />
          </el-descriptions-item> -->
          <el-descriptions-item class="formItem" label="机构名称">
            <DictSelect
              v-model="formData.jgmc"
              :showCode="false"
              type="dynamic"
              :params="{ configId: 'jzjd_jgmc_dynamic' }"
            />
          </el-descriptions-item>
          <el-descriptions-item class="formItem" label=""></el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="content-body" v-if="activeStep === 2">
        <CardList :personList="personList" @update:checkedPersons="handleCheckedPersonsChange" />
      </div>
      <div class="content-body" v-if="activeStep === 3">
        <el-input v-model="filterText" style="width: 240px" placeholder="查询相关材料" />
        <el-tree
          ref="treeRef"
          style="max-width: 600px"
          :data="treeData"
          show-checkbox
          node-key="id"
          highlight-current
          :props="defaultProps"
          @check-change="handleCheckChange"
          :filter-node-method="filterNode"
        />
      </div>
    </div>
    <div class="stepButton">
      <el-button v-if="activeStep !== 1" @click="handlePrev">上一步</el-button>
      <el-button
        v-if="fsywStore.mode !== 'view' && activeStep === 3"
        type="primary"
        @click="handleSave"
        >保存
      </el-button>
      <el-button type="primary" v-if="activeStep !== 3" @click="handleNext">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, watch } from 'vue'
import DictSelect from '@/components/dict/DictSelect.vue'
import CardList from './CardList.vue'
import { useJdywStore } from '@/views/jdyw/stores/jdywfs.js'
import { getUrlParams } from '@/utils/common.js'
import { ElMessage } from 'element-plus'

const fsywStore = useJdywStore()
const params = getUrlParams()
const formData = reactive({})
const activeStep = ref(1)
const title = ref('选择业务类型')
const filterText = ref('')
const personList = ref([])
const treeRef = ref(null)
const treeData = ref([])
const loading = ref(false)

watch(filterText, (val) => {
  treeRef.value?.filter(val)
})

const defaultProps = {
  children: 'children',
  label: 'nodeName'
}

const checkedPersons = ref([])

const handleCheckedPersonsChange = (persons) => {
  checkedPersons.value = persons
  // 在这里可以对勾选的人员数据进行处理
  console.log('勾选的人员数据:', checkedPersons.value)
}

const handleCheckChange = (data, checked, indeterminate) => {
  console.log(data, checked, indeterminate)
}

const handleNext = () => {
  if (activeStep.value === 1) {
    if (!formData.jgmc) {
      ElMessage.error('请选择机构名称')
      return
    } else if (!formData.ywlx) {
      ElMessage.error('请选择业务类型')
      return
    }
  }

  activeStep.value = activeStep.value + 1
  title.value = activeStep.value === 2 ? '选择对象' : '相关材料'
}
const handlePrev = () => {
  activeStep.value = activeStep.value - 1
  title.value = activeStep.value === 2 ? '选择对象' : '选择业务类型'
}
const handleSave = async () => {
  console.log('保存')
  loading.value = true
  console.log(formData)
  console.log(checkedPersons.value)
  const checkNode = treeRef.value?.getCheckedNodes(true)
  console.log(checkNode)
  const dxbh = checkedPersons.value.map((item) => item.rybh).join(',')
  const form = { ...formData, dxbh }
  const postData = {
    business: form,
    dossiers: checkNode
  }

  const data = await fsywStore.saveFsForm(postData)
  if (data && data.systemid) {
    ElMessage({
      showClose: true,
      message: '发送成功',
      type: 'success'
    })
    fsywStore.addDialogVisble = false
  }
  loading.value = false
}

const filterNode = (value, data) => {
  if (!value) return true
  return data.nodeName.includes(value)
}

const filterNodes = (node) => {
  return node.filter((item) => {
    if (item.children && item.children.length > 0) {
      if (item.parentId === 'root') {
        item.disabled = true
      }
      item.children = filterNodes(item.children)
      return true
    } else {
      return false
    }
  })
}

onMounted(async () => {
  loading.value = true

  if (fsywStore.mode === 'add') {
    const { AJBH } = params
    await fsywStore.getInitData({ AJBH })
    await fsywStore.getAJInfo({ AJBH })
    await fsywStore.getTreeData({ AJBH })
    treeData.value = filterNodes(fsywStore.treeData)
    formData.ajmc = fsywStore.AJInfoData?.detail
    formData.ajbh = fsywStore.AJInfoData?.code
    personList.value = fsywStore.initData.map((item) => {
      return {
        checked: false,
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        name: item.xm,
        idCard: item.zjhm,
        birthDate: item.csrq,
        status: item.rystate,
        id: item.systemid,
        rybh: item.rybh
      }
    })
  } else {
    // 使用 Object.assign 将 fsFormData 的属性复制给 formData
    Object.assign(formData, fsywStore?.fsFormData?.business)
    await fsywStore.getInitData({ AJBH: formData.ajbh })
    await fsywStore.getTreeData({ AJBH: formData.ajbh })
    treeData.value = filterNodes(fsywStore.treeData)
    console.log('formData', formData)
    console.log('fsywStore.initData', fsywStore.initData)
    personList.value = fsywStore.initData.map((item) => {
      return {
        checked: formData.dxbh.indexOf(item.systemid) > -1,
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        name: item.xm,
        idCard: item.zjhm,
        birthDate: item.csrq,
        status: item.rystate,
        id: item.systemid,
        rybh: item.rybh
      }
    })
  }
  loading.value = false
})
</script>

<style lang="less" scoped>
.container {
  padding: 6px;
  background-color: #fff;
  min-height: 500px;

  :deep(.contentStyle) {
    width: 240px !important;
  }
  :deep(.is-bordered-label) {
    color: red;
  }
}

.content {
  width: 75%;
  margin: auto;
}

.title {
  margin-top: 24px;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 700;
}

.cell-item {
  display: flex;
  align-items: center;
}

.formBody {
  margin-top: 24px;
  //   display: flex;
}

.formItem {
  //   flex: 1;
  color: red;
}

.stepButton {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-select__wrapper) {
  box-shadow: none !important;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focused) {
  box-shadow: none !important;
}

:deep(.el-select__wrapper.is-focused) {
  box-shadow: none !important;
}
</style>
