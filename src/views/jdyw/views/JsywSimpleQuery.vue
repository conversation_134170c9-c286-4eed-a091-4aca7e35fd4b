<template>
  <div class="container">
    <SimpleQuery
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button size="small" type="primary" @click="view(prop.row)">查看</el-button>
      </template>

    </SimpleQuery>
    <el-dialog v-model="jdywStore.addDialogVisible" :show-close="false" width="80%">
      <template #header="{ close, titleId }">
        <DialogTitle title="提前解除强制戒毒意见" @close="close" :id="titleId" />
      </template>
<!--      <FsywAddForm />-->
      <JSYWviewForm :formId="jdywStore.viewJSId" />
    </el-dialog>



    <el-dialog v-model="jdywStore.jslcVisible" :show-close="false" width="80%">
      <template #header="{ close, titleId }">
        <DialogTitle title="材料查看" @close="close" :id="titleId" />
      </template>
      <!--      <FsywAddForm />-->
      <JsclSimpleQuery :id="jdywStore.viewJSId" />
    </el-dialog>


  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import DialogTitle from '@/components/dialogTitle/index.vue'
import UseConfig from '../configs/query-B_ASJ_JZJD_YJWS202403211611'
import { useJdywStore } from '../stores/jdywjs.js'
import { getUrlParams } from '@/utils/common.js'
import JSYWviewForm from '@/views/jdyw/views/JSYWviewForm.vue'
import JsclSimpleQuery from '@/views/jdyw/views/JsclSimpleQuery.vue'


const params = getUrlParams();

// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
const jdywStore = useJdywStore()
const view = (row) => {
  console.log(row)
  jdywStore.viewJSId = row.SYSTEMID;
  jdywStore.addDialogVisible = true;
}

</script>

<style lang="less" scoped>
.container {
  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
