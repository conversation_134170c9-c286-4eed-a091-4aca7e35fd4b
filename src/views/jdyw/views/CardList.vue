<template>
  <div class="card-list">
    <el-row :gutter="20">
      <el-col :span="8" v-for="(person, index) in props.personList" :key="index">
        <el-card class="card-item" :body-style="{ padding: '20px' }">
          <el-checkbox
            v-model="person.checked"
            @change="handleCheckboxChange"
            class="card-checkbox"
          >
            <div class="card-content">
              <div class="avatar">
                <el-avatar :src="person.avatar" :size="60" fit="cover"></el-avatar>
              </div>
              <div class="info">
                <h3 class="name">{{ person.name }}</h3>
                <p class="id-card">身份证: {{ person.idCard }}</p>
                <p class="birthday">出生日期: {{ person.birthDate }}</p>
                <div class="status-container">
                  <span class="status-label">人员状态:</span>
                  <el-tag :type="person.status === '在逃' ? 'danger' : 'success'" effect="plain">{{
                      person.status
                    }}
                  </el-tag>
                </div>
                <!--                <p class="id">人员编号: {{ truncate(person.rybh, 10) }}</p>-->
                <p class="id" style="width: 230px;">人员编号: {{ person.rybh }}</p>
              </div>
            </div>
          </el-checkbox>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  personList: {
    type: Array,
    required: true
  }
})


const emit = defineEmits(['update:checkedPersons'])

const handleCheckboxChange = () => {
  const checkedPersons = props.personList.filter((person) => person.checked)
  emit('update:checkedPersons', checkedPersons)
}


const truncate = (str, length) => {
  if (str.length > length) {
    return str.slice(0, length) + '...'
  } else {
    return str
  }
}

// const personList = ref([
//   {
//     checked: false,
//     avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
//     name: '张三',
//     idCard: '123456789012345678',
//     birthDate: '1990-01-01',
//     status: '在逃',
//     id: '001'
//   },
//   {
//     checked: false,
//     avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
//     name: '李四',
//     idCard: '987654321098765432',
//     birthDate: '1985-05-15',
//     status: '抓获',
//     id: '002'
//   }
//   // 添加更多人员数据
// ])
</script>

<style scoped>
.card-list {
  margin: 20px;
}

.card-item {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  min-height: 148px; /* 设置卡片高度 */
  display: flex; /* 启用 flex 布局 */
  align-items: center; /* 垂直居中对齐 */
}

.card-item:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-checkbox {
  display: flex;
  align-items: center;
  width: 100%; /* 使复选框占满整个卡片宽度 */
}

.card-content {
  display: flex;
  align-items: center;
  width: 100%; /* 使内容区域占满整个卡片宽度 */
}

.avatar {
  margin-right: 20px;
}

.info {
  font-size: 14px;
  flex: 1; /* 使信息区域占满剩余空间 */
}

.name {
  font-weight: bold;
  margin-bottom: 10px;
}

.id-card,
.birthday,
.id {
  margin-bottom: 5px;
  color: #666;
}

.status-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.status-label {
  color: #666;
  margin-right: 5px;
}
</style>
