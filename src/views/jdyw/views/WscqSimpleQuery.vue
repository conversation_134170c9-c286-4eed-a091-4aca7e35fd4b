<template>
  <div class="container">
    <SimpleQuery
      :queryId="id"
      :queryConditions="condition.list"
      :tableColumns="column.list"
      :operations="operation.list"
      :query-params="params"
    >
      <template #cz="prop">
        <el-button v-if="!prop.row?.CQSJ" size="small" type="primary" @click="view(prop.row,'cq')">呈请</el-button>
        <el-button v-if="prop.row?.CQSJ" size="small" type="primary" @click="view(prop.row,'ck')">查看</el-button>
      </template>
    </SimpleQuery>
  </div>
</template>

<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import UseConfig from '../configs/query-V_ASJ_JZJD_YJWS202403291600'
import { useSpxxStore } from '../stores/spxx.js'
import { getUrlParams } from '@/utils/common.js'
import router from '@/router/index.js'

const params = getUrlParams()

// eslint-disable-next-line no-undef

const { id, operation, condition, column } = UseConfig()
const csxxStore = useSpxxStore()
const view = (row, type) => {
  console.log(row)
  csxxStore.viewJSId = row.SYSTEMID

  window.open(`/#/approval?wsid=${row.SYSTEMID}&type=${type}`, '_blank')

  // csxxStore.dialogVisible = true;
  // router.push({
  //   name: 'approval',
  //   query: {
  //     wsid: row.SYSTEMID,
  //     type: type
  //   }
  // })
}
</script>

<style lang="less" scoped>
.container {
  padding: 22px;

  :deep(.el-dialog) {
    --el-dialog-padding-primary: 0;
  }
}
</style>
