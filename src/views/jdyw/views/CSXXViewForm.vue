<template>
  <div class="container" v-loading="loading">
    <el-descriptions title="详细信息" border :column="3" size="default">
      <el-descriptions-item label="案件编号">{{ formData.ajbh }}</el-descriptions-item>
      <el-descriptions-item label="对象编号">{{ formData.dxbh }}</el-descriptions-item>
      <el-descriptions-item label="对象姓名">{{ formData.xm }}</el-descriptions-item>
      <el-descriptions-item label="对象性别">{{ formData.xb }}</el-descriptions-item>
      <el-descriptions-item label="出所日期">{{ formData.csrq }}</el-descriptions-item>
      <el-descriptions-item label="出所衔接负责人姓名">{{ formData.csxjr }}</el-descriptions-item>
      <el-descriptions-item label="出所衔接负责人联系方式">{{
        formData.csxjrlxfs
      }}</el-descriptions-item>
      <el-descriptions-item label="是否执行">{{ formData.sfzx }}</el-descriptions-item>
      <el-descriptions-item label="执行方式">{{ formData.zxfs }}</el-descriptions-item>
      <el-descriptions-item label="执行场所">{{ formData.zxcs }}</el-descriptions-item>
      <el-descriptions-item label="场所衔接负责人姓名">{{ formData.cscsxjr }}</el-descriptions-item>
      <el-descriptions-item label="场所衔接负责人联系方式">{{
        formData.cscsxjrlxfs
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useCsxxStore } from '@/views/jdyw/stores/csxx.js'

const loading = ref(false)
const formData = ref({})
const csxxStore = useCsxxStore()
const props = defineProps({
  formId: {
    type: String,
    required: true
  }
})

onMounted(async () => {
  loading.value = true
  const res = await csxxStore.getViewFormData({ systemid: props.formId })
  formData.value = res
  loading.value = false
})
</script>

<style scoped lang="less">
.container {
  width: 80%;
  padding: 20px;
  margin: auto;
}
</style>
