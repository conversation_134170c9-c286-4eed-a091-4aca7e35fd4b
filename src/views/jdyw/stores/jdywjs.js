import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as jsywService from '@/views/jdyw/services/jsyw.js'

export const useJdywStore = defineStore('jdywjs', () => {

  const viewJSId = ref('')
  const addDialogVisble = ref(false)
  const jslcVisible = ref(false)

  const getViewFormData = async (data) => {
    const res = await jsywService.getViewFormData(data)
    return res
  }

  return { addDialogVisble,jslcVisible, viewJSId, getViewFormData }
})
