import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as jsywService from '@/views/jdyw/services/jsyw.js'

export const usePGYJStore = defineStore('pgyj', () => {

  const viewJSId = ref('')
  const pgyjVisible = ref(false)

  const getViewFormData = async (data) => {
    const res = await jsywService.getViewFormData(data)
    return res
  }

  return { pgyjVisible, viewJSId, getViewFormData }
})
