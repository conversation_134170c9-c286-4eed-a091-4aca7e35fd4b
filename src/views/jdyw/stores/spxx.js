import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as jsywService from '@/views/jdyw/services/spxx.js'

export const useSpxxStore = defineStore('spxx', () => {

  const viewJSId = ref('')
  const dialogVisible = ref(false)

  const getWorkFlowIdData = async (data) => {
    const res = await jsywService.getWorkFlowIdData(data)
    return res
  }


  const getNextUsers = async (data) => {
    return await jsywService.getNextUsers(data)
  }

  const getWsPDF = async (data) => {
    return await jsywService.getWsPDF(data)
  }


  const getWorkItemId = async (data) => {
    return await jsywService.getWorkItemId(data)
  }

  const postToNextSP = async (data) => {
    return await jsywService.postToNextSP(data)
  }


  return { dialogVisible, viewJSId, getWorkFlowIdData,getNextUsers,getWsPDF,getWorkItemId,postToNextSP }
})
