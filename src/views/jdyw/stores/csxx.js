import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as csxxService from '@/views/jdyw/services/csxx.js'

export const useCsxxStore = defineStore('csxx', () => {
  const viewJSId = ref('')
  const csxxVisible = ref(false)

  const getViewFormData = async (data) => {
    const res = await csxxService.getViewFormData(data)
    return res
  }

  return { csxxVisible, viewJSId, getViewFormData }
})
