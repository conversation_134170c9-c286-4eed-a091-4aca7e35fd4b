import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as fsywService from '@/views/jdyw/services/fsyw.js'

export const useJdywStore = defineStore('jdywfs', () => {
  const addDialogVisible = ref(false)


  const initData = ref([])
  const AJInfoData = ref({})
  const treeData = ref({})
  const fsFormData = ref({})
  const mode = ref('add')

  const getInitData = async (payload) => {
    const res = await fsywService.getInitData(payload)
    if (res) {
      initData.value = res
    }
  }
  const getAJInfo = async (payload) => {
    const res = await fsywService.getAJInfo(payload)
    if (res) {
      AJInfoData.value = res?.dictItems?.[0]
    }
  }

  const getTreeData = async (payload) => {
    const res = await fsywService.getTreeData(payload)
    if (res) {
      treeData.value = res
    }
  }

  const saveFsForm = async (payload) => {
    return await fsywService.saveFsForm(payload)

  }

  const getRowData = async (payload) => {
    console.log('获取表单数据')
    fsFormData.value = await fsywService.getRowData(payload)
  }

  const deleteRowData = async (payload) => {
    console.log('获取表单数据')
    await fsywService.deleteRowData(payload)
  }




  return {
    mode,
    deleteRowData,
    getRowData,
    addDialogVisible,
    getInitData,
    getAJInfo,
    initData,
    AJInfoData,
    getTreeData,
    treeData,
    saveFsForm,
    fsFormData
  }
})
