import { useRequest } from '@/utils/request/useRequest.js'

const { get, post, _delete } = useRequest()


export const getInitData = async ({ AJBH }) => {
  return await get(`/api/b_asj_jzjd_business/getxyrxx/ajbh/${AJBH}`)
}

export const getTreeData = async ({ AJBH }) => {
  return await get(`/api/b_asj_jzjd_business/getdzzj/ajbh/${AJBH}`)
}

export const saveFsForm = async (data) => {
  return await post(`/api/b_asj_jzjd_business/send`, data)
}

export const getRowData = async (systemid) => {
  return await get(`/api/b_asj_jzjd_business/getfsxx/systemid/${systemid}`)
}

export const deleteRowData = async (systemid) => {
  return await _delete (`/api/b_asj_jzjd_business/${systemid}`)
}


export const getAJInfo = async ({ AJBH }) => {

  const commonParams = {
    searchField: 'detail',
    page: 1,
    pageSize: 10
  }

  return await get(`/DynamicDict/load`, {
    configId: 'b_asj_aj',
    ajbh: AJBH,
    ...commonParams
  })
}
