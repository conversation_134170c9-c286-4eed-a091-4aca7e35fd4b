import { useRequest } from '@/utils/request/useRequest.js'

const { get, post } = useRequest()


export const getWorkFlowIdData = async (systemid) => {
  return await get(`/api/jzjd/workflow/getFlowInfo/${systemid}`)
}


export const getNextUsers = async (systemid) => {
  return await get(`/api/jzjd/workflow/getNextUsers/${systemid}`)
}


export const getWsPDF = async (systemid) => {
  return await get(`/api/b_asj_jzjd_refiles/getByYjwsId/${systemid}`, null, {
    responseType: 'blob'
  })
}


export const getWorkItemId = async (wsId) => {
  return await post(`/api/jzjd/workflow/commitjob`,{wsId});
}

export const postToNextSP = async (data) => {
  return await post(`/api/jzjd/workflow/commitjob`,data);
}







