// import { handleAdd } from '../funcs/B_ASJ_JZJD_YJWS202403211611'

const UseConfig = (ref) => {
  return {
    id: 'V_ASJ_JZJD_YJWS202403291600',
    title: '文书呈请',
    operation: {
      list: []
    },
    condition: {
      list: [
        //
        // {
        //   cname: '业务类型',
        //   colName: 'YWLX',
        //     inputType: 'dict',
        //     type: 'dynamic',
        //     params: {
        //       configId: 'jzjd_fs_ywlx'
        //     }
        // },
        // {
        //   // 日期范围输入框
        //   cname: '接收时间',
        //   colName: 'CREATEDTIME',
        //   inputType: 'dateRange'
        // },
      ]
    },
    column: {
      list: [
        {
          colName: 'YWDM',
          cname: '业务类型'
        },
        {
          colName: 'DXBH',
          cname: '对象'
        },
        {
          colName: 'ZXCS',
          cname: '执行场所'
        },
        {
          colName: 'JGMC',
          cname: '机构名称'
        },
        {
          colName: 'CQSJ',
          cname: '申请时间'
        },
        {
          colName: 'SPSJ',
          cname: '审批时间'
        },
        {
          colName: 'SPJG',
          cname: '审批结果'
        },
        {
          colName: 'SPR',
          cname: '审批人'
        },
        {
          cname: '操作',
          colName: 'cz',
          slot: 'cz',
          width: 140
        }
      ]
    }
  }
}

export default UseConfig
