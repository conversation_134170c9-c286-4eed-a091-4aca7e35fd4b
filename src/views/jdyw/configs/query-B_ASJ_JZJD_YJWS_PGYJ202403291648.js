// import { handleAdd } from '../funcs/B_ASJ_JZJD_YJWS202403211611'

const UseConfig = (ref) => {
  return {
    id: 'B_ASJ_JZJD_YJWS_PGYJ202403291648',
    title: '评估意见查询',
    operation: {
      list: []
    },
    condition: {
      list: [
        //
        // {
        //   cname: '业务类型',
        //   colName: 'YWLX',
        //     inputType: 'dict',
        //     type: 'dynamic',
        //     params: {
        //       configId: 'jzjd_fs_ywlx'
        //     }
        // },
        // {
        //   // 日期范围输入框
        //   cname: '接收时间',
        //   colName: 'CREATEDTIME',
        //   inputType: 'dateRange'
        // },
      ]
    },
    column: {
      list: [
        {
          colName: 'SYSTEMID',
          cname: '唯一编号',
          width: 250
          // slot: 'RYBH'
        },
        {
          colName: 'YWDM',
          cname: '业务类型'
        },
        {
          colName: 'XM',
          cname: '对象'
        },

        {
          cname: '操作',
          colName: 'cz',
          slot: 'cz',
          width: 140
        }
      ]
    }
  }
}

export default UseConfig
