// import { handleAdd } from '../funcs/B_ASJ_JZJD_YJWS202403211611'

const UseConfig = (ref) => {
  return {
    id: 'V_ASJ_JZJD_YJWSSP202403291600',
    title: '审批信息',
    operation: {
      list: [

      ]
    },
    condition: {
      list: [
        //
        // {
        //   cname: '业务类型',
        //   colName: 'YWLX',
        //     inputType: 'dict',
        //     type: 'dynamic',
        //     params: {
        //       configId: 'jzjd_fs_ywlx'
        //     }
        // },
        // {
        //   // 日期范围输入框
        //   cname: '接收时间',
        //   colName: 'CREATEDTIME',
        //   inputType: 'dateRange'
        // },
      ]
    },
    column: {
      list: [
        {
          colName: 'XM',
          cname: '对象'
        },
        {
          colName: 'AJBH',
          cname: '案件编号'
        },
        {
          colName: 'AJMC',
          cname: '案件名称'
        },
        {
          colName: 'YWDM',
          cname: '业务类型'
        },
        {
          colName: 'ACTIVITYTITLE',
          cname: '当前环节'
        },
        {
          colName: 'SPHISTORYAPPROVETIME',
          cname: '审批时间'
        },{
          colName: 'SPHISTORYRESULT',
          cname: '审批结果'
        },{
          colName: 'SPHISTORYCONTENT',
          cname: '审批意见'
        },{
          colName: 'SPHISTORYSPR',
          cname: '审批人'
        },
        {
          cname: '操作',
          colName: 'cz',
          slot: 'cz',
          width: 140
        }
      ]
    }
  }
}

export default UseConfig
