import { handleAdd } from '../funcs/V_B_JZJD_BUSINESS_JB202403211648'

const UseConfig = (ref) => {
  return {
    id: 'V_B_JZJD_BUSINESS_JB202403211648',
    title: '发送业务查询',
    operation: {
      list: [
        {
          name: '添加',
          type: 'primary',
          handle: handleAdd.bind(null, ref)
        }
      ]
    },
    condition: {
      list: [
        {
          cname: '申请时间',
          colName: 'CREATEDTIME',
          inputType: 'dateRange'
        },
        {
          cname: '业务类型',
          colName: 'YWDM',
          inputType: 'dict',
          type: 'static',
          kind: 'jzjd_ywlx'
        }
      ]
    },
    column: {
      list: [
        {
          colName: 'YWDM',
          cname: '业务类型',
          width: 250
          // slot: 'YWDM'
        },
        {
          colName: 'DXBH',
          cname: '对象',
          width: 250
        },
        {
          colName: 'JGMC',
          cname: '机构名称'
        },
        {
          colName: 'CREATEDTIME',
          cname: '申请时间'
        },
        {
          colName: 'SENDTIME',
          cname: '推送时间'
        },
        {
          colName: 'SENDFLAG',
          cname: '是否成功'
        },
        {
          colName: 'YWZT',
          cname: '业务状态'
        },
        {
          cname: '操作',
          colName: 'cz',
          slot: 'cz',
          width: 140
        }
      ]
    }
  }
}

export default UseConfig
