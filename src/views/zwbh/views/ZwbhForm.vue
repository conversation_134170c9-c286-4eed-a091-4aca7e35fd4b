<template>
  <div class="container" v-loading="loading">
    <HeaderTitle title="指纹保护表单"></HeaderTitle>
    <el-form
      ref="ruleFormRef"
      :rules="rules"
      label-width="auto"
      :model="formData"
      :inline="true"
      :disabled="isViewMode"
      style="max-width: 1600px; margin: auto; margin-top: 20px"
    >
      <table class="table">
        <tr>
          <td class="titleTd requiredTd">人员编号</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="rybh">
              <el-input v-model="formData.rybh" placeholder="请输入人员编号" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">人员类型</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="xszt_type_name">
              <el-input v-model="formData.xszt_type_name" placeholder="请输入人员类型" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">姓名</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="xm">
              <el-input v-model="formData.xm" placeholder="请输入姓名" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">性别</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="xb">
              <DictSelect
                v-model="formData.xb"
                :showCode="false"
                type="static"
                kind="01"
                placeholder="请选择性别"
              />
            </el-form-item>
          </td>
          <td class="titleTd">身份证号码</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="zjhm">
              <el-input v-model="formData.zjhm" placeholder="请输入身份证号码" />
            </el-form-item>
          </td>
          <td class="titleTd">户籍所在地区划</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="xzqh">
              <DictSelect
                v-model="formData.xzqh"
                :showCode="false"
                type="static"
                kind="07"
                placeholder="请选择户籍所在地区划"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">户籍地址</td>
          <td class="inputTd" colspan="3">
            <el-form-item style="width: 100%" prop="hjdz">
              <el-input v-model="formData.hjdz" placeholder="请输入户籍地址" />
            </el-form-item>
          </td>
          <td class="titleTd">主办民警</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="ajzbry">
              <DictSelect
                v-model="formData.ajzbry"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
                placeholder="请选择主办民警"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">协办民警</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="ajxbry">
              <DictSelect
                v-model="formData.ajxbry"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
                placeholder="请选择协办民警"
              />
            </el-form-item>
          </td>
          <td class="titleTd">对比类型</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="bzlx_name">
              <el-input v-model="formData.bzlx_name" placeholder="请输入对比类型" />
            </el-form-item>
          </td>
          <td class="titleTd">对比结果</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="wxsm">
              <el-input v-model="formData.wxsm" placeholder="请输入对比结果" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">对比时间</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="bzsj">
              <el-date-picker
                v-model="formData.bzsj"
                type="datetime"
                placeholder="请选择对比时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </td>
          <td class="titleTd" colspan="4"></td>
        </tr>
      </table>
    </el-form>
    <div class="btn-area">
      <el-button v-if="!systemid && !isViewMode" link @click="resetForm">重置</el-button>
      <el-button v-if="!isViewMode" type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
      <el-button v-if="isViewMode" @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRequest } from '@/utils/request/useRequest'
import { ElMessage } from 'element-plus'
import HeaderTitle from '@/components/headerTitle/index.vue'
import DictSelect from '@/components/dict/DictSelect.vue'
import { getNormalUrlParams } from '@/utils/common.js'

const { post, get } = useRequest()
const loading = ref(false)
const ruleFormRef = ref()
const systemid = ref('')
const isViewMode = ref(false)

// 获取URL参数
const params = getNormalUrlParams()
const { type, systemid: urlSystemid } = params

// 表单数据
const formData = reactive({
  rybh: '',
  xszt_type_name: '',
  xm: '',
  xb: '',
  zjhm: '',
  hjdz: '',
  xzqh: '',
  ajzbry: '',
  ajxbry: '',
  bzlx_name: '',
  wxsm: '',
  bzsj: ''
})

// 表单验证规则
const rules = {
  rybh: [{ required: true, message: '请输入人员编号', trigger: 'blur' }],
  xszt_type_name: [{ required: true, message: '请输入人员类型', trigger: 'blur' }],
  xm: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
}

// 初始化
onMounted(async () => {
  if (urlSystemid) {
    systemid.value = urlSystemid
  }

  if (type === 'view') {
    isViewMode.value = true
  }

  if (systemid.value) {
    await loadFormData()
  }
})

// 加载表单数据
const loadFormData = async () => {
  loading.value = true
  try {
    const response = await get(`/zwbh/detail/${systemid.value}`)
    if (response) {
      Object.assign(formData, response)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async (formRef) => {
  if (!formRef) return

  await formRef.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true
      try {
        const url = systemid.value ? `/zwbh/update/${systemid.value}` : '/zwbh/add'

        await post(url, formData)
        ElMessage.success(systemid.value ? '更新成功' : '添加成功')

        // 如果是新增，跳转到查询页面
        if (!systemid.value) {
          window.close()
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      } finally {
        loading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 重置表单
const resetForm = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.resetFields()
}

// 关闭页面
const handleClose = () => {
  window.close()
}
</script>

<style scoped>
.container {
  width: 100%;
  padding: 20px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.table td {
  border: 1px solid #dcdfe6;
  padding: 12px;
  vertical-align: top;
}

.titleTd {
  background-color: #f5f7fa;
  font-weight: bold;
  width: 120px;
  text-align: right;
}

.requiredTd::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.inputTd {
  width: 200px;
}

.btn-area {
  text-align: center;
  margin-top: 20px;
}

.btn-area .el-button {
  margin: 0 10px;
}
</style>
