<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'

const handleAdd = () => {
  window.open(`/#/zwbhForm?type=add`, '_blank')
}

const handleEdit = ({ SYSTEMID }) => {
  window.open(`/#/zwbhForm?systemid=${SYSTEMID}&type=edit`, '_blank')
}

const handleView = ({ SYSTEMID }) => {
  window.open(`/#/zwbhForm?systemid=${SYSTEMID}&type=view`, '_blank')
}

// 查询条件配置
const queryConditions = [
  {
    cname: '人员编号',
    colName: 'RYBH',
    inputType: 'string'
  },
  {
    cname: '人员类型',
    colName: 'XSZT_TYPE_NAME',
    inputType: 'string'
  },
  {
    cname: '姓名',
    colName: 'XM',
    inputType: 'string'
  },
  {
    cname: '性别',
    colName: 'XB',
    inputType: 'dict',
    type: 'static',
    kind: '01'
  },
  {
    cname: '身份证号码',
    colName: 'ZJHM',
    inputType: 'string'
  },
  {
    cname: '户籍地址',
    colName: 'HJDZ',
    inputType: 'string'
  },
  {
    cname: '户籍所在地区划',
    colName: 'XZQH',
    inputType: 'dict',
    type: 'static',
    kind: '07'
  },
  {
    cname: '主办民警',
    colName: 'AJZBRY',
    inputType: 'dict',
    type: 'dynamic',
    params: {
      configId: '10002'
    }
  },
  {
    cname: '协办民警',
    colName: 'AJXBRY',
    inputType: 'dict',
    type: 'dynamic',
    params: {
      configId: '10002'
    }
  },
  {
    cname: '对比类型',
    colName: 'BZLX_NAME',
    inputType: 'string'
  },
  {
    cname: '对比结果',
    colName: 'WXSM',
    inputType: 'string'
  },
  {
    cname: '对比时间',
    colName: 'BZSJ',
    inputType: 'dateRange'
  }
]

// 简表ID
const queryId = 'V_SWXXBD_ZWHMXX20250707164800'

// 表格列配置
const tableColumns = [
  {
    colName: 'RYBH',
    cname: '人员编号',
    slot: 'RYBH'
  },
  {
    colName: 'XSZT_TYPE_NAME',
    cname: '人员类型'
  },
  {
    colName: 'XM',
    cname: '姓名'
  },
  {
    colName: 'XB',
    cname: '性别'
  },
  {
    colName: 'ZJHM',
    cname: '身份证号码'
  },
  {
    colName: 'HJDZ',
    cname: '户籍地址'
  },
  {
    colName: 'XZQH',
    cname: '户籍所在地区划'
  },
  {
    colName: 'AJZBRY',
    cname: '主办民警'
  },
  {
    colName: 'AJXBRY',
    cname: '协办民警'
  },
  {
    colName: 'BZLX_NAME',
    cname: '对比类型'
  },
  {
    colName: 'WXSM',
    cname: '对比结果'
  },
  {
    colName: 'BZSJ',
    cname: '对比时间'
  },
  {
    cname: '操作',
    colName: 'cz',
    slot: 'cz',
    width: 200
  }
]

// 操作按钮配置
const operations = [
  {
    name: '新增',
    type: 'primary',
    handle: handleAdd
  }
]
</script>

<template>
  <HeaderTitle title="比对查询"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #RYBH="prop">
          <a href="javascript:void(0)" @click="handleView(prop.row)">{{ prop.row.RYBH }}</a>
        </template>
        <template #cz="prop">
          <el-button size="small" @click="handleView(prop.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(prop.row)">编辑</el-button>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
