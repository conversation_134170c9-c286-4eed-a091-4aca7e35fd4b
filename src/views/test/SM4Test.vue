<template>
  <div class="sm4-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>SM4加密测试工具</span>
        </div>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="姓名:">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="手机号:">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        
        <el-form-item label="身份证号:">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        
        <el-form-item label="加密密钥:">
          <el-input v-model="form.key" placeholder="请输入加密密钥（可选）" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="generateSM4Code">生成SM4代码</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="testLogin" :disabled="!encryptedCode">测试登录</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div v-if="originalData" class="result-section">
        <h4>原始数据:</h4>
        <el-input
          v-model="originalData"
          type="textarea"
          :rows="2"
          readonly
          class="result-input"
        />
        
        <h4>加密后的SM4代码:</h4>
        <el-input
          v-model="encryptedCode"
          type="textarea"
          :rows="3"
          readonly
          class="result-input"
        />
        
        <div class="action-buttons">
          <el-button @click="copyToClipboard(encryptedCode)" size="small">
            <el-icon><CopyDocument /></el-icon>
            复制SM4代码
          </el-button>
          <el-button @click="copyTestUrl" size="small" type="primary">
            <el-icon><Link /></el-icon>
            复制测试URL
          </el-button>
        </div>
      </div>
      
      <el-divider />
      
      <div class="preset-section">
        <h4>预设测试数据:</h4>
        <el-button @click="loadPresetData" size="small" type="info">
          加载示例数据 (gymj01,13005928409,530323200102030050)
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument, Link } from '@element-plus/icons-vue'
import { generateTestSM4Code } from '@/utils/crypto/sm4.js'

const form = reactive({
  name: '',
  phone: '',
  idCard: '',
  key: 'defaultkey123456' // 默认密钥
})

const originalData = ref('')
const encryptedCode = ref('')

// 生成SM4代码
const generateSM4Code = () => {
  if (!form.name || !form.phone || !form.idCard) {
    ElMessage.warning('请填写完整的姓名、手机号和身份证号')
    return
  }
  
  try {
    const data = `${form.name},${form.phone},${form.idCard}`
    originalData.value = data
    encryptedCode.value = generateTestSM4Code(form.name, form.phone, form.idCard, form.key)
    ElMessage.success('SM4代码生成成功')
  } catch (error) {
    console.error('生成SM4代码失败:', error)
    ElMessage.error('生成SM4代码失败')
  }
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.phone = ''
  form.idCard = ''
  form.key = 'defaultkey123456'
  originalData.value = ''
  encryptedCode.value = ''
}

// 加载预设数据
const loadPresetData = () => {
  form.name = 'gymj01'
  form.phone = '13005928409'
  form.idCard = '530323200102030050'
  generateSM4Code()
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 复制测试URL
const copyTestUrl = async () => {
  if (!encryptedCode.value) {
    ElMessage.warning('请先生成SM4代码')
    return
  }
  
  const baseUrl = window.location.origin + window.location.pathname
  const testUrl = `${baseUrl}#/?sm4Code=${encodeURIComponent(encryptedCode.value)}`
  
  try {
    await navigator.clipboard.writeText(testUrl)
    ElMessage.success('测试URL已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 测试登录
const testLogin = () => {
  if (!encryptedCode.value) {
    ElMessage.warning('请先生成SM4代码')
    return
  }
  
  const testUrl = `/?sm4Code=${encodeURIComponent(encryptedCode.value)}`
  window.open(testUrl, '_blank')
}

// 页面加载时自动加载示例数据
loadPresetData()
</script>

<style scoped>
.sm4-test-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.test-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.result-section {
  margin-top: 20px;
}

.result-section h4 {
  margin: 15px 0 10px 0;
  color: #409eff;
}

.result-input {
  margin-bottom: 15px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.preset-section {
  margin-top: 20px;
}

.preset-section h4 {
  margin: 15px 0 10px 0;
  color: #67c23a;
}
</style>
