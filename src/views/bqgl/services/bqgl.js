import { useRequest } from '@/utils/request/useRequest'

const { post } = useRequest()

/**
 * 标签管理相关接口
 */
export const bqglService = {
  /**
   * 分页查询标签记录
   * @param {Object} params 查询参数
   * @param {number} params.pageNum 页码
   * @param {number} params.pageSize 每页大小
   * @param {string} params.type 类型
   * @param {string} params.value 查询值（身份证号）
   * @returns {Promise} 查询结果
   */
  queryForPage: (params) => {
    // 将参数转换为URL查询字符串
    const queryString = new URLSearchParams(params).toString()
    const url = `http://148.0.6.127:8083/bqpt-web/rhbq/bqjl/queryForPage?${queryString}`

    // 使用POST方法，但参数在URL中，请求体为空
    return post(
      url,
      [
        { bm: 'CZRK_PDBZ', bqbm: 'CZRK_PDBZ', bqlx: '01', values: ['1'], mc: '常住人口标识: 是' },
        { bm: 'rhbq', bqbm: 'rhbq', bqlx: '6', values: [params.value], mc: '' }
      ],
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
