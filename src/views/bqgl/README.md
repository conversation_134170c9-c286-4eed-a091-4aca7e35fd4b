# 标签管理功能

## 功能概述

标签管理页面提供了对人员标签信息的查询和管理功能，包括：

- 按身份证号和姓名查询标签信息
- 展示人员的标签列表和详细信息
- 支持分页查询
- 提供详情查看功能

## 页面结构

### 文件结构

```
src/views/bqgl/
├── services/
│   └── bqgl.js          # 标签管理相关接口
├── views/
│   └── BqglView.vue     # 标签管理主页面
└── README.md            # 说明文档
```

### 路由配置

- 路径：`/bqgl`
- 组件：`BqglView.vue`
- 需要认证：是

## 功能特性

### 1. 查询功能

- **身份证号查询**：支持按身份证号精确查询
- **姓名查询**：支持按姓名模糊查询
- **重置功能**：一键清空查询条件

### 2. 数据展示

- **身份证号**：显示人员身份证号
- **姓名**：显示人员姓名
- **标签列表**：以彩色标签形式展示所有标签
- **详细信息**：展示人员的所有基本信息

### 3. 标签显示

- 标签使用不同颜色区分不同类型
- 颜色配置来自后端返回的 `labelColors` 字段
- 标签内容来自 `labels` 字段

### 4. 详细信息

- 展示 `showList` 中的所有字段信息
- 包括：姓名、性别、出生日期、民族、地址等
- 支持查看详情弹窗

### 5. 导出功能

- **表格导出**：导出当前页面所有数据为Excel文件
- **详情导出**：导出单条记录的详细信息为Excel文件
- **文件命名**：自动生成带日期的文件名
- **数据格式**：包含基本信息和标签信息

## 接口说明

### 查询接口

- **URL**：`http://148.0.6.127:8083/bqpt-web/rhbq/bqjl/queryForPage`
- **方法**：POST
- **参数**：通过URL查询字符串传递
  - `pageNum`：页码
  - `pageSize`：每页大小
  - `type`：类型（固定为 '01'）
  - `value`：查询值（身份证号）
- **请求体**：空
- **示例URL**：`http://148.0.6.127:8083/bqpt-web/rhbq/bqjl/queryForPage?pageNum=1&pageSize=10&type=01&value=452225199506200021`

### 响应数据结构

```json
{
  "code": 200,
  "msg": "成功",
  "data": {
    "totalRow": 1,
    "list": [
      {
        "bqwybs": "身份证号",
        "labels": ["标签1", "标签2"],
        "labelColors": ["255,0,0", "0,255,0"],
        "showList": [
          {
            "zdmc": "字段名",
            "value": "字段值",
            "zd": "字段标识"
          }
        ]
      }
    ]
  }
}
```

## 使用说明

### 1. 访问页面

在浏览器中访问 `/#/bqgl` 或通过导航菜单点击"标签管理"

### 2. 查询数据

1. 在查询条件中输入身份证号或姓名
2. 点击"查询"按钮
3. 查看查询结果

### 3. 查看详情

1. 在表格中点击"查看详情"按钮
2. 在弹出的详情窗口中查看完整信息

### 4. 分页操作

- 使用分页控件切换页面
- 调整每页显示数量

### 5. 导出数据

- **导出表格**：点击查询条件区域的"导出表格"按钮，导出当前页面所有数据
- **导出详情**：在表格操作列或详情弹窗中点击"导出详情"按钮，导出单条记录
- **文件下载**：Excel文件会自动下载到浏览器默认下载目录
- **文件命名**：格式为"标签管理数据*日期.xlsx"或"姓名*详情\_日期.xlsx"

## 注意事项

1. **跨域问题**：如果遇到跨域问题，需要在后端配置CORS或使用代理服务器
2. **数据格式**：确保后端返回的数据格式符合预期
3. **权限控制**：页面需要登录认证才能访问
4. **性能优化**：大量数据时建议使用虚拟滚动

## 开发说明

### 添加新功能

1. 在 `services/bqgl.js` 中添加新的接口方法
2. 在 `BqglView.vue` 中添加对应的功能
3. 更新路由配置（如需要）

### 错误处理

- 接口调用失败时会显示错误提示
- 网络错误会自动重试
- 数据为空时会显示相应提示

## 样式定制

页面使用了 Element Plus 组件库，可以通过修改 CSS 变量或直接修改样式来自定义外观：

```less
// 自定义标签颜色
.label-tag {
  // 自定义样式
}

// 自定义表格样式
.table-container {
  // 自定义样式
}
```

## 部署说明

### 生产环境配置

1. 确保后端接口地址正确
2. 配置适当的代理或CORS设置
3. 检查网络连接和防火墙设置

### 调试建议

1. 使用浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 验证接口返回的数据格式
