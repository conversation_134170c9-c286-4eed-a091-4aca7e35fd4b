<template>
  <div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">标签管理</h2>
    </div>

    <!-- 查询条件 -->
    <div class="query-form">
      <el-form :model="queryForm" :inline="true" @submit.prevent="handleQuery">
        <el-form-item label="身份证号">
          <el-input
            v-model="queryForm.zjhm"
            placeholder="请输入身份证号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input
            v-model="queryForm.xm"
            placeholder="请输入姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading"> 查询 </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="exportTableData" :loading="exportLoading"
            >导出表格</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table :data="tableData" v-loading="loading" border stripe style="width: 100%">
        <el-table-column prop="bqwybs" label="身份证号" width="180" />
        <el-table-column label="姓名" width="120">
          <template #default="{ row }">
            <el-button
              type="text"
              @click="handleView(row)"
              style="padding: 0; color: #409eff; text-decoration: underline"
            >
              {{ getShowListValue(row.showList, 'xm') }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="300">
          <template #default="{ row }">
            <div class="labels-container">
              <el-tag
                v-for="(label, index) in row.labels"
                :key="index"
                :style="{ backgroundColor: `rgb(${row.labelColors[index]})` }"
                class="label-tag"
              >
                {{ label }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="详细信息" min-width="400">
          <template #default="{ row }">
            <div class="info-container">
              <div v-for="(item, index) in row.showList" :key="index" class="info-item">
                <span class="info-label">{{ item.zdmc }}:</span>
                <span class="info-value">{{ item.value || '-' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleView(row)"> 查看详情 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="详细信息"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div v-for="(item, index) in currentDetail.showList" :key="index" class="detail-item">
              <span class="detail-label">{{ item.zdmc }}:</span>
              <span class="detail-value">{{ item.value || '-' }}</span>
            </div>
          </div>
        </div>
        <div class="detail-section">
          <h4>标签信息</h4>
          <div class="labels-detail">
            <el-tag
              v-for="(label, index) in currentDetail.labels"
              :key="index"
              :style="{ backgroundColor: `rgb(${currentDetail.labelColors[index]})` }"
              class="label-tag"
            >
              {{ label }}
            </el-tag>
          </div>
        </div>
        <div class="detail-actions">
          <el-button type="success" @click="exportDetailData(currentDetail)">导出详情</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { bqglService } from '../services/bqgl'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailVisible = ref(false)
const currentDetail = ref(null)
const exportLoading = ref(false)

// 查询表单
const queryForm = reactive({
  zjhm: '',
  xm: ''
})

// 获取showList中指定字段的值
const getShowListValue = (showList, field) => {
  const item = showList.find((item) => item.zd === field)
  return item ? item.value : '-'
}

// 查询数据
const queryData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      type: '01'
    }

    // 如果有身份证号，添加到查询参数
    if (queryForm.zjhm) {
      params.value = queryForm.zjhm
    }

    const response = await bqglService.queryForPage(params)

    if (response.code === 200) {
      tableData.value = response.data.list || []
      total.value = response.data.totalRow || 0

      // 如果有姓名查询条件，在前端过滤
      if (queryForm.xm) {
        tableData.value = tableData.value.filter((item) => {
          const name = getShowListValue(item.showList, 'xm')
          return name && name.includes(queryForm.xm)
        })
      }
    } else {
      ElMessage.error(response.msg || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮点击
const handleQuery = () => {
  currentPage.value = 1
  queryData()
}

// 重置按钮点击
const handleReset = () => {
  queryForm.zjhm = ''
  queryForm.xm = ''
  currentPage.value = 1
  queryData()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val
  queryData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  currentPage.value = val
  queryData()
}

// 查看详情
const handleView = (row) => {
  currentDetail.value = row
  detailVisible.value = true
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  detailVisible.value = false
  currentDetail.value = null
}

// 导出表格数据为Excel
const exportTableData = () => {
  exportLoading.value = true
  try {
    // 准备表格数据
    const exportData = tableData.value.map((row) => {
      const data = {
        身份证号: row.bqwybs,
        姓名: getShowListValue(row.showList, 'xm'),
        性别: getShowListValue(row.showList, 'xbdm'),
        出生日期: getShowListValue(row.showList, 'csrq'),
        民族: getShowListValue(row.showList, 'mzdm'),
        户籍地址: getShowListValue(row.showList, 'hjdz_dzmc'),
        现住址: getShowListValue(row.showList, 'xzz_dzmc'),
        标签: row.labels ? row.labels.join(', ') : ''
      }

      // 添加showList中的其他字段
      row.showList.forEach((item) => {
        if (!['xm', 'xbdm', 'csrq', 'mzdm', 'hjdz_dzmc', 'xzz_dzmc'].includes(item.zd)) {
          data[item.zdmc] = item.value || '-'
        }
      })

      return data
    })

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 身份证号
      { wch: 10 }, // 姓名
      { wch: 8 }, // 性别
      { wch: 12 }, // 出生日期
      { wch: 10 }, // 民族
      { wch: 30 }, // 户籍地址
      { wch: 30 }, // 现住址
      { wch: 50 } // 标签
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '标签管理数据')

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    saveAs(blob, `标签管理数据_${new Date().toISOString().split('T')[0]}.xlsx`)

    ElMessage.success('表格数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 导出详情数据为Excel
const exportDetailData = (row) => {
  exportLoading.value = true
  try {
    // 准备详情数据
    const detailData = []

    // 基本信息
    detailData.push(['基本信息'])
    detailData.push(['字段名', '字段值'])
    row.showList.forEach((item) => {
      detailData.push([item.zdmc, item.value || '-'])
    })

    // 标签信息
    detailData.push([])
    detailData.push(['标签信息'])
    detailData.push(['标签名称'])
    row.labels.forEach((label) => {
      detailData.push([label])
    })

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(detailData)

    // 设置列宽
    ws['!cols'] = [{ wch: 20 }, { wch: 30 }]

    // 添加工作表到工作簿
    const name = getShowListValue(row.showList, 'xm') || row.bqwybs
    XLSX.utils.book_append_sheet(wb, ws, `${name}_详情`)

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    saveAs(blob, `${name}_详情_${new Date().toISOString().split('T')[0]}.xlsx`)

    ElMessage.success('详情数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 组件挂载时查询数据
onMounted(() => {
  queryData()
})
</script>

<style lang="less" scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .page-title {
    margin: 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
    border-left: 4px solid #409eff;
    padding-left: 12px;
  }
}

.query-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.labels-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.label-tag {
  color: white;
  border: none;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.info-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.info-label {
  font-weight: bold;
  margin-right: 4px;
  color: #666;
}

.info-value {
  color: #333;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-content {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      margin-bottom: 12px;
      color: #333;
      border-bottom: 2px solid #409eff;
      padding-bottom: 4px;
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .detail-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .detail-label {
    font-weight: bold;
    margin-right: 8px;
    color: #666;
    min-width: 80px;
  }

  .detail-value {
    color: #333;
    flex: 1;
  }

  .labels-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .detail-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
