import { useRequest } from '@/utils/request/useRequest.js'

const { get, post, put, delete: del } = useRequest()

export const getJsxForm = async ({ systemid }) => {
  return await get(`/api/b_baxz_jsx/${systemid}`)
}

export const saveJsxForm = async (data) => {
  return await post(`/api/b_baxz_jsx`, data)
}

export const updateJsxForm = async (data) => {
  return await put(`/api/b_baxz_jsx/${data.systemid}`, data)
}

export const deleteJsxForm = async (systemid) => {
  return await del(`/api/b_baxz_jsx/${systemid}`)
}

// 获取案件信息
export const getAjInfo = async (ajbh) => {
  return await get(`${window.config.ajblContextPath}/api/b_asj_ajs/${ajbh}/ajs`)
}

// 获取翻译后的介绍信数据
export const getJsxTranslate = async (systemid) => {
  return await get(`/api/b_baxz_jsx/translate/${systemid}`)
}
