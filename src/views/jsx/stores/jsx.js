import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as jsxService from '@/views/jsx/services/jsx.js'

export const useJsxStore = defineStore('jsx', () => {
  const jsxForm = ref({})
  const addDialogVisible = ref(false)
  const mode = ref('add') // add, edit, view
  const ajInfo = ref({}) // 案件信息
  const jsxTranslateData = ref({}) // 翻译后的介绍信数据

  const saveJsxForm = async (data) => {
    const res = await jsxService.saveJsxForm(data)
    return res
  }

  const updateJsxForm = async (data) => {
    const res = await jsxService.updateJsxForm(data)
    return res
  }

  const getJsxForm = async ({ systemid }) => {
    const res = await jsxService.getJsxForm({ systemid })
    jsxForm.value = res
    return res
  }

  const deleteJsxForm = async (systemid) => {
    const res = await jsxService.deleteJsxForm(systemid)
    return res
  }

  // 获取案件信息
  const getAjInfo = async (ajbh) => {
    const res = await jsxService.getAjInfo(ajbh)
    ajInfo.value = res
    return res
  }

  // 获取翻译后的介绍信数据
  const getJsxTranslate = async (systemid) => {
    const res = await jsxService.getJsxTranslate(systemid)
    jsxTranslateData.value = res
    return res
  }

  return {
    jsxForm,
    addDialogVisible,
    mode,
    ajInfo,
    jsxTranslateData,
    saveJsxForm,
    getJsxForm,
    updateJsxForm,
    deleteJsxForm,
    getAjInfo,
    getJsxTranslate
  }
})
