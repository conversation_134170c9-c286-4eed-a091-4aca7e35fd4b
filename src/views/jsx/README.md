# 介绍信管理模块

## 功能说明

本模块提供介绍信的完整管理功能，包括：

- 介绍信列表查询
- 新增介绍信
- 编辑介绍信
- 删除介绍信
- 查看介绍信详情
- **案件信息自动获取**：根据案件编号自动获取案件信息并填充表单
- **介绍信导出打印**：编辑模式下可导出标准格式的介绍信并打印

## 文件结构

```
src/views/jsx/
├── services/
│   └── jsx.js          # API 服务层
├── stores/
│   └── jsx.js          # 状态管理
├── views/
│   ├── JsxQuery.vue    # 列表查询页面
│   └── JsxForm.vue     # 表单页面（新增/编辑/查看）
└── README.md           # 说明文档
```

## 功能特性

### 1. 列表查询页面 (JsxQuery.vue)

- 支持按处理时间和业务类型查询
- 显示案件编号、案件名称、主办民警等字段
- 提供新增、编辑、删除操作
- 案件编号支持点击跳转到编辑页面
- **支持URL参数传递**：自动获取URL上的ajbh参数并传递给新增页面

### 2. 表单页面 (JsxForm.vue)

- 支持新增、编辑、查看三种模式
- 包含完整的表单验证
- 自动获取当前用户信息作为录入人和录入单位
- 支持字典选择器（民警、单位等）
- **案件信息自动获取**：
  - 页面加载时自动获取URL上的ajbh参数
  - 调用案件信息接口获取详细信息
  - 自动填充案件编号、案件名称、主办民警、协办民警等字段
- **介绍信导出打印**：
  - 仅在编辑模式下显示"导出介绍信"按钮
  - 生成标准格式的介绍信
  - 支持直接打印功能

### 3. 字段说明

- **案件编号** (AJBH): 必填，文本输入，支持自动获取
- **案件名称** (AJMC): 必填，文本输入，支持自动获取
- **主办民警** (AJZBRY): 必填，字典选择，支持自动获取
- **协办民警** (ZJXBRY): 可选，字典选择，支持自动获取
- **主办单位** (ZBDW): 必填，字典选择
- **申请民警** (SQMJ): 必填，字典选择
- **对接单位** (DJDW): 必填，字典选择
- **申请事由** (SQSY): 必填，多行文本
- **录入人** (CREATOR): 自动获取，只读
- **录入单位** (DJDW): 自动获取，只读

## 使用方法

### 访问列表页面

```
/#/jsx
```

### 新增介绍信

```
/#/jsxForm?type=add
```

### 新增介绍信（带案件编号）

```
/#/jsxForm?type=add&ajbh=A3707035800002025040003
```

### 编辑介绍信

```
/#/jsxForm?systemid=123&type=edit
```

### 查看介绍信

```
/#/jsxForm?systemid=123&type=view
```

### 从案件页面跳转到介绍信新增

```
/#/jsx?ajbh=A3707035800002025040003
```

## API 接口

- `GET /api/b_baxz_jsx/{systemid}` - 获取介绍信详情
- `POST /api/b_baxz_jsx` - 新增介绍信
- `PUT /api/b_baxz_jsx/{systemid}` - 更新介绍信
- `DELETE /api/b_baxz_jsx/{systemid}` - 删除介绍信
- `GET /api/b_asj_ajs/{ajbh}/ajs` - 获取案件信息
- `GET /api/b_baxz_jsx/translate/{systemid}` - 获取翻译后的介绍信数据

## 案件信息自动获取功能

### 功能说明

当页面URL包含ajbh参数时，系统会自动调用案件信息接口获取案件详情，并自动填充以下字段：

- 案件编号 (AJBH)
- 案件名称 (AJMC)
- 案件主办民警 (AJZBRY)
- 协办民警 (ZJXBRY)

### 使用场景

1. 从案件详情页面跳转到介绍信新增页面
2. 从其他业务模块传递案件编号到介绍信模块
3. 快速创建与特定案件相关的介绍信

### 接口地址

```
GET http://192.168.18.4:31781/api/b_asj_ajs/{ajbh}/ajs
```

### 参数说明

- `ajbh`: 案件编号，从URL参数获取

## 介绍信导出打印功能

### 功能说明

在编辑模式下，用户可以点击"导出介绍信"按钮生成标准格式的介绍信并打印。系统会调用翻译接口获取处理后的数据，确保介绍信内容的准确性和规范性。

### 介绍信格式

```
[对接单位] 介绍信

[对接单位]：

兹介绍 [主办单位] [主办民警] [协办民警] 系 警员 前往你处 办理 [案件名称]（[案件编号]） 请接洽。望能给予协助及支持。

此致
敬礼！

[主办单位名称]
[当前日期]
有效期（）天
```

### 翻译数据处理

- 系统会调用 `/api/b_baxz_jsx/translate/{systemid}` 接口获取翻译后的数据
- 翻译后的数据会替换原始表单数据，确保介绍信内容的准确性
- 如果翻译接口失败，会使用原始表单数据作为备选

### 使用步骤

1. 进入编辑模式（`/#/jsxForm?systemid=123&type=edit`）
2. 确保必要字段已填写完整
3. 点击"导出介绍信"按钮
4. 系统会自动打开打印预览窗口
5. 确认内容无误后点击打印

### 必要字段检查

导出前系统会检查以下字段是否已填写：

- 对接单位 (DJDW)
- 主办单位 (ZBDW)
- 主办民警 (AJZBRY)
- 案件名称 (AJMC)
- 案件编号 (AJBH)

### 打印样式

- 使用宋体字体，确保正式文档的规范性
- 标准A4纸张格式
- 包含边框和适当的间距
- 支持打印机设置调整

## 注意事项

1. 删除操作会弹出确认对话框
2. 表单提交前会进行必填字段验证
3. 编辑和查看模式需要传入 systemid 参数
4. 新增模式下会自动获取当前用户信息
5. 操作完成后会自动关闭窗口并刷新列表
6. **案件信息获取失败时会显示警告提示，用户可手动填写**
7. **支持从其他页面传递ajbh参数，实现业务流转**
8. **导出介绍信功能仅在编辑模式下可用**
9. **打印前请确保打印机已连接并正常工作**
10. **介绍信格式符合公安机关正式文档规范**
