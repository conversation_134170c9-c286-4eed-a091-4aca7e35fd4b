<template>
  <div class="container" v-loading="loading">
    <HeaderTitle title="介绍信表单"></HeaderTitle>
    <el-form
      ref="ruleFormRef"
      :rules="rules"
      label-width="auto"
      :model="formData"
      :inline="true"
      :disabled="isViewMode"
      style="max-width: 1600px; margin: auto; margin-top: 20px"
    >
      <table class="table">
        <tr>
          <td class="titleTd requiredTd">案件编号</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="ajbh">
              <el-input v-model="formData.ajbh" placeholder="请输入案件编号" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">案件名称</td>
          <td class="inputTd" colspan="3">
            <el-form-item style="width: 100%" prop="ajmc">
              <el-input v-model="formData.ajmc" placeholder="请输入案件名称" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">主办民警</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="ajzbry">
              <DictSelect
                v-model="formData.ajzbry"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd">协办民警</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="zjxbry">
              <DictSelect
                v-model="formData.zjxbry"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">申请民警</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="sqmj">
              <DictSelect
                v-model="formData.sqmj"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">主办单位</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="zbdw">
              <DictSelect v-model="formData.zbdw" :showCode="false" type="static" kind="06" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">对接单位</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="djdw">
              <DictSelect v-model="formData.djdw" :showCode="false" type="static" kind="06" />
            </el-form-item>
          </td>
          <td class="titleTd" colspan="2"></td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">申请事由</td>
          <td class="inputTd" colspan="5">
            <el-form-item style="width: 100%" prop="sqsy">
              <el-input
                v-model="formData.sqsy"
                type="textarea"
                :rows="4"
                placeholder="请输入申请事由"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">录入人</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="creator">
              <DictSelect
                disabled
                v-model="formData.creator"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd">录入单位</td>
          <td class="inputTd">
            <el-form-item style="width: 100%" prop="djdw">
              <DictSelect
                v-model="formData.djdw"
                disabled
                :showCode="false"
                type="static"
                kind="06"
              />
            </el-form-item>
          </td>
          <td class="titleTd" colspan="2"></td>
        </tr>
      </table>
    </el-form>
    <div class="btn-area">
      <el-button v-if="!systemid && !isViewMode" link @click="resetForm">重置</el-button>
      <el-button v-if="!isViewMode" type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
      <el-button v-if="isViewMode" @click="handleClose">关闭</el-button>
      <el-button v-if="systemid && !isViewMode" type="success" @click="exportIntroductionLetter">
        导出介绍信
      </el-button>
    </div>

    <!-- 打印区域 -->
    <div id="printArea" style="display: none">
      <div class="letter-container">
        <div class="letter-header">
          <h2>{{ jsxStore.jsxTranslateData?.zbdw || formData.zbdw }} 介绍信</h2>
        </div>

        <div class="letter-content">
          <p class="recipient">{{ jsxStore.jsxTranslateData?.djdw || formData.djdw }}：</p>

          <div class="letter-body">
            <p>
              兹介绍 <strong>{{ jsxStore.jsxTranslateData?.zbdw || formData.zbdw }}</strong>
              <strong>{{ jsxStore.jsxTranslateData?.ajzbry || formData.ajzbry }}</strong>
              <span v-if="jsxStore.jsxTranslateData?.zjxbry || formData.zjxbry"
                >、<strong>{{ jsxStore.jsxTranslateData?.zjxbry || formData.zjxbry }}</strong></span
              >
              系 警员 前往你处 办理
              <strong
                >{{ jsxStore.jsxTranslateData?.ajmc || formData.ajmc }}（{{
                  jsxStore.jsxTranslateData?.ajbh || formData.ajbh
                }}）</strong
              >
              请接洽。望能给予协助及支持。
            </p>
          </div>

          <div class="letter-footer">
            <p style="text-indent: 2em">此致</p>
            <p>敬礼！</p>
          </div>

          <div class="letter-signature">
            <p>{{ jsxStore.jsxTranslateData?.zbdw || formData.zbdw }}</p>
            <p>{{ currentDate }}</p>
            <p class="validity-period">有效期（<span class="validity-input"></span>）天</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useJsxStore } from '@/views/jsx/stores/jsx.js'
import { ElMessage } from 'element-plus'
import { getNormalUrlParams } from '@/utils/common.js'
import HeaderTitle from '@/components/headerTitle/index.vue'
import DictSelect from '@/components/dict/DictSelect.vue'

const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
const params = getNormalUrlParams()
const loading = ref(false)
const systemid = params.systemid
const ajbh = params.ajbh // 获取URL上的ajbh参数
const isViewMode = ref(params.type === 'view')
const formData = ref({})
const ruleFormRef = ref()
const jsxStore = useJsxStore()

// 计算当前日期
const currentDate = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}年${month}月${day}日`
})

const rules = reactive({
  ajbh: [{ required: true, message: '请输入案件编号', trigger: 'blur' }],
  ajmc: [{ required: true, message: '请输入案件名称', trigger: 'blur' }],
  ajzbry: [{ required: true, message: '请选择主办民警', trigger: 'change' }],
  sqmj: [{ required: true, message: '请选择申请民警', trigger: 'change' }],
  zbdw: [{ required: true, message: '请选择主办单位', trigger: 'change' }],
  djdw: [{ required: true, message: '请选择对接单位', trigger: 'change' }],
  sqsy: [{ required: true, message: '请输入申请事由', trigger: 'blur' }]
})

const resetForm = () => {
  formData.value = {
    ajbh: '',
    ajmc: '',
    ajzbry: '',
    zjxbry: '',
    zbdw: '',
    sqmj: userInfo?.loginUserDTO?.id || '',
    sqsy: '',
    creator: userInfo?.loginUserDTO?.id || '',
    djdw: userInfo?.loginUserDTO?.workdept || ''
  }
}

const handleClose = () => {
  window.close()
}

// 导出介绍信
const exportIntroductionLetter = async () => {
  // 检查必要字段是否填写
  if (
    !formData.value.djdw ||
    !formData.value.zbdw ||
    !formData.value.ajzbry ||
    !formData.value.ajmc ||
    !formData.value.ajbh
  ) {
    ElMessage.warning('请先完善必要信息后再导出介绍信')
    return
  }

  try {
    loading.value = true
    // 获取翻译后的介绍信数据
    await jsxStore.getJsxTranslate(systemid)
    // 打印介绍信
    printLetter()
  } catch (error) {
    console.error('获取翻译数据失败:', error)
    ElMessage.error('获取介绍信数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 打印介绍信
const printLetter = () => {
  const printWindow = window.open('', '_blank')
  const printContent = document.getElementById('printArea').innerHTML

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>介绍信</title>
      <style>
        body {
          font-family: 'SimSun', serif;
          margin: 0;
          padding: 20px;
          font-size: 16px;
          line-height: 1.6;
        }
        .letter-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
          border: 2px solid #000;
          min-height: 500px;
        }
        .letter-header {
          text-align: center;
          margin-bottom: 40px;
          border-bottom: 2px solid #000;
          padding-bottom: 20px;
        }
        .letter-header h2 {
          margin: 0;
          font-size: 32px;
          font-weight: bold;
        }
        .letter-content {
          padding: 20px 0;
        }
        .recipient {
          font-size: 22px;
          font-weight: bold;
          margin-bottom: 30px;
        }
        .letter-body {
          margin: 30px 0;
          text-indent: 2em;
          font-size: 18px;
        }
        .letter-footer {
          margin: 40px 0 20px 0;
        }
        .letter-footer p {
          margin: 10px 0;
          font-size: 18px;
        }
        .letter-signature {
          margin-top: 60px;
        }
        .letter-signature p {
          margin: 10px 0;
          font-size: 16px;
        }
        .letter-signature p:first-child,
        .letter-signature p:nth-child(2) {
          text-align: right;
        }
        .validity-period {
          text-align: left;
          font-size: 16px;
        }
        .validity-input {
          border-bottom: 1px solid #000;
          padding: 0 20px;
          min-width: 60px;
          display: inline-block;
        }
        @media print {
          body {
            padding: 0;
          }
          .letter-container {
            border: none;
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `)

  printWindow.document.close()
  printWindow.focus()

  // 等待内容加载完成后打印
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
  }, 500)
}

// 获取案件信息并填充表单
const loadAjInfo = async (ajbh) => {
  if (!ajbh) return

  try {
    loading.value = true
    const ajInfo = await jsxStore.getAjInfo(ajbh)

    // 根据接口返回的数据结构填充表单
    if (ajInfo) {
      // 假设接口返回的数据结构包含案件信息
      // 根据实际接口返回的数据结构调整字段映射
      formData.value.ajbh = ajInfo.ajbh || ajbh // 案件编号
      formData.value.ajmc = ajInfo.ajmc || '' // 案件名称
      formData.value.ajzbry = ajInfo.ajzbry || '' // 案件主办民警
      formData.value.zjxbry = ajInfo.ajxbry || '' // 协办民警
      formData.value.zbdw = ajInfo.zbdw || '' // 主办单位
      console.log('案件信息加载成功:', ajInfo)
    }
  } catch (error) {
    console.error('获取案件信息失败:', error)
    ElMessage.warning('获取案件信息失败，请手动填写')
  } finally {
    loading.value = false
  }
}

const submitForm = (formEl) => {
  if (!formEl) return
  loading.value = true

  formEl.validate((valid) => {
    if (valid) {
      if (formData.value.systemid) {
        // 编辑模式
        jsxStore
          .updateJsxForm(formData.value)
          .then(() => {
            loading.value = false
            ElMessage.success('修改成功')
            window.close()
          })
          .catch(() => {
            loading.value = false
            ElMessage.error('修改失败')
          })
      } else {
        // 新增模式
        formData.value.creator = userInfo?.loginUserDTO?.id || ''
        formData.value.djdw = userInfo?.loginUserDTO?.workdept || ''
        jsxStore
          .saveJsxForm(formData.value)
          .then((res) => {
            loading.value = false
            ElMessage.success('保存成功')
            formData.value.systemid = res.systemid
            window.close()
          })
          .catch(() => {
            loading.value = false
            ElMessage.error('保存失败')
          })
      }
    } else {
      console.log('error submit!')
      loading.value = false
    }
  })
}

onMounted(async () => {
  if (systemid) {
    // 编辑或查看模式
    loading.value = true
    try {
      await jsxStore.getJsxForm({ systemid })
      formData.value = { ...jsxStore.jsxForm }
    } catch (error) {
      ElMessage.error('获取数据失败')
    }
    loading.value = false
  } else {
    // 新增模式
    resetForm()

    // 如果有ajbh参数，获取案件信息
    if (ajbh) {
      await loadAjInfo(ajbh)
    }
  }
})
</script>

<style lang="less" scoped>
.container {
  padding: 20px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;

  .titleTd {
    width: 120px;
    padding: 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    font-weight: bold;
    text-align: right;
    vertical-align: top;

    &.requiredTd::before {
      content: '*';
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .inputTd {
    padding: 12px;
    border: 1px solid #e4e7ed;
    background-color: #fff;
  }
}

.btn-area {
  text-align: center;
  margin-top: 20px;

  .el-button {
    margin: 0 10px;
  }
}

// 打印样式
.letter-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  border: 2px solid #000;
  min-height: 500px;
}

.letter-header {
  text-align: center;
  margin-bottom: 40px;
  border-bottom: 2px solid #000;
  padding-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
  }
}

.letter-content {
  padding: 20px 0;
}

.recipient {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 30px;
}

.letter-body {
  margin: 30px 0;
  text-indent: 2em;
  font-size: 18px;
}

.letter-footer {
  margin: 40px 0 20px 0;

  p {
    margin: 10px 0;
    font-size: 18px;
  }
}

.letter-signature {
  margin-top: 60px;

  p {
    margin: 10px 0;
    font-size: 16px;
  }

  p:first-child,
  p:nth-child(2) {
    text-align: right;
  }
}

.validity-period {
  text-align: left;
  font-size: 16px;

  .validity-input {
    border-bottom: 1px solid #000;
    padding: 0 20px;
    min-width: 60px;
    display: inline-block;
  }
}
</style>
