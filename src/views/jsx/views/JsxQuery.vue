<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import { ref } from 'vue'
import { useJsxStore } from '@/views/jsx/stores/jsx.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getNormalUrlParams } from '@/utils/common.js'

const jsxStore = useJsxStore()
const queryTableRef = ref()
const params = getNormalUrlParams()

const handleAdd = () => {
  // 获取URL上的ajbh参数
  const { ajbh } = params
  if (ajbh) {
    window.open(`/#/jsxForm?type=add&ajbh=${ajbh}`, '_blank')
  } else {
    window.open(`/#/jsxForm?type=add`, '_blank')
  }
}

const handleEdit = (row) => {
  window.open(`/#/jsxForm?systemid=${row.SYSTEMID}&type=edit`, '_blank')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await jsxStore.deleteJsxForm(row.SYSTEMID)
    ElMessage.success('删除成功')
    // 刷新表格数据
    if (queryTableRef.value) {
      await queryTableRef.value.queryTableData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const queryConditions = [
  {
    cname: '处理时间',
    colName: 'RESERVATION20',
    inputType: 'dateRange'
  },
  {
    cname: '业务类型',
    colName: 'RESERVATION10',
    inputType: 'dict',
    type: 'static',
    kind: 'ASJ_YS_PFYW'
  }
]

const queryId = 'B_BAXZ_JSX20250708165000'

const tableColumns = [
  // {
  //   colName: 'SYSTEMID',
  //   cname: '系统编号',
  //   width: 120,
  //   show: false
  // },
  {
    colName: 'AJBH',
    cname: '案件编号',
    slot: 'AJBH'
  },
  {
    colName: 'AJMC',
    cname: '案件名称'
  },
  {
    colName: 'AJZBRY',
    cname: '主办民警'
  },
  {
    colName: 'ZJXBRY',
    cname: '协办民警'
  },
  {
    colName: 'ZBDW',
    cname: '主办单位'
  },
  {
    colName: 'SQMJ',
    cname: '申请民警'
  },
  {
    colName: 'DJDW',
    cname: '对接单位'
  },
  {
    colName: 'SQSY',
    cname: '申请事由'
  },
  {
    cname: '操作',
    colName: 'cz',
    slot: 'cz',
    width: 140
  }
]

const operations = [
  {
    name: '新增',
    type: 'primary',
    handle: handleAdd
  }
]
</script>

<template>
  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        ref="queryTableRef"
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #cz="prop">
          <el-button size="small" @click="handleEdit(prop.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(prop.row)">删除</el-button>
        </template>
        <template #AJBH="prop">
          <a href="javascript:void(0)" @click="handleEdit(prop.row)">{{ prop.row.AJBH }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
