<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
// import QueryForm from '@/components/simpleQuery/queryForm.vue'
import { ref, getCurrentInstance } from 'vue'
const selectedValue = ref('htmj')

const handleAdd = () => {
  console.log('添加')
}

const handleEdit = (row) => {
  console.log(row)
}

const handleDelete = (row) => {
  console.log(row)
}

const queryConditions = [
  {
    cname: '实际移送检察院时间',
    colName: 'RESERVATION20',
    inputType: 'dateRange'
  },
  {
    cname: '批复业务',
    colName: 'RESERVATION10',
    inputType: 'dict',
    type: 'static',
    kind: 'ASJ_YS_PFYW'
  }
]

const queryId = 'B_ASJ_JFXX202004271347'

const tableColumns = [
  {
    colName: 'AJBH',
    cname: '案件编号',
    width: 250,
    slot: 'AJBH'
  },
  {
    colName: 'RYBH',
    cname: '人员编号',
    width: 250
  },
  // {
  //   colName: 'RESERVATION20',
  //   cname: '实际移送检察院时间'
  // },
  {
    colName: 'RESERVATION10',
    cname: '批复业务'
  },
  {
    colName: 'RESERVATION11',
    cname: '决定类型'
  },
  {
    colName: 'PZ_JDSWH',
    cname: '决定书文号'
  },
  {
    colName: 'PZ_JDJG',
    cname: '决定机关'
  },
  {
    colName: 'CREATEDTIME',
    cname: '录入时间'
  },
  {
    colName: 'PZ_DPSJ',
    cname: '决定时间'
  },
  {
    cname: '操作',
    colName: 'cz',
    slot: 'cz',
    width: 140
  }
]

const operations = [
  {
    name: '添加',
    type: 'primary',
    handle: handleAdd
  }
  // {
  //   name: '删除',
  //   type: 'danger',
  //   size: 'small',
  //   handle: handleDelete
  // }
]
</script>

<template>
  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #cz="prop">
          <el-button size="small" @click="handleEdit(prop.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(prop.row)">删除</el-button>
        </template>
        <template #AJBH="prop">
          <a href="javascript:void(0)" @click="handleEdit(prop.row)">{{ prop.row.AJBH }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
