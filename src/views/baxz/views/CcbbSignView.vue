<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'
import { useCcbbStore } from '@/views/baxz/stores/ccbb.js'
import { ElMessage } from 'element-plus'

const ccbbStore = useCcbbStore()

const handleView = ({ SYSTEMID }) => {
  window.open(`/#/ccbbForm?systemid=${SYSTEMID}&type=view`, '_blank')
}

const handleSign = async (row) => {
  try {
    await ccbbStore.signCcbbForm(row.SYSTEMID)
    ElMessage.success('签收成功')
    // 刷新列表
    window.location.reload()
  } catch (error) {
    ElMessage.error('签收失败')
  }
}

const queryConditions = []

const queryId = 'B_BAXZ_CCBB20250512175000'

const tableColumns = [
  {
    colName: 'SYSTEMID',
    cname: '出差编号'
  },
  {
    colName: 'CCMJ',
    cname: '出差民警'
  },
  {
    colName: 'SXMJ',
    cname: '随行民警'
  },
  {
    colName: 'CCSX',
    cname: '出差事项'
  },
  {
    colName: 'XZDW',
    cname: '协助单位'
  },
  {
    colName: 'QSZT',
    cname: '签收状态'
  },
  {
    colName: 'LRR',
    cname: '录入人'
  },
  {
    colName: 'CREATEDTIME',
    cname: '录入时间'
  },
  {
    colName: 'CZ',
    cname: '操作',
    slot: 'cz'
  }
]

const operations = []
</script>

<template>
  <HeaderTitle title="出差报备签收"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #cz="prop">
          <el-button size="small" type="primary" @click="handleView(prop.row)">查看</el-button>
          <el-button
            size="small"
            type="success"
            @click="handleSign(prop.row)"
            v-if="prop.row.ZT === '0' || !prop.row.ZT"
            >签收</el-button
          >
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
