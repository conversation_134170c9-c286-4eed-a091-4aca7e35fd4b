<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'

const queryConditions = [
  {
    cname: '出差民警',
    colName: 'CCMJ',
    inputType: 'dict',
    type: 'static',
    kind: '10002'
  },
  {
    cname: '出差事项',
    colName: 'CCSX',
    inputType: 'dict',
    type: 'static',
    kind: 'baxz_qqxzsx'
  },
  {
    cname: '前往地市',
    colName: 'QWDS',
    inputType: 'dict',
    type: 'static',
    kind: 'ssds'
  },
  {
    cname: '协助单位',
    colName: 'XZDW',
    inputType: 'dict',
    type: 'static',
    kind: '06'
  },
  {
    cname: '录入时间',
    colName: 'CREATEDTIME',
    inputType: 'dateRange'
  }
]

const queryId = 'B_BAXZ_CCBB20250522100000'

const tableColumns = [
  {
    colName: 'SYSTEMID',
    cname: '出差编号',
    slot: 'BH'
  },
  {
    colName: 'CCMJ',
    cname: '出差民警'
  },
  {
    colName: 'SXMJ',
    cname: '随行民警'
  },
  {
    colName: 'CCSX',
    cname: '出差事项'
  },
  {
    colName: 'XZDW',
    cname: '协助单位'
  },
  {
    colName: 'DDSJ',
    cname: '到达时间'
  },
  {
    colName: 'QSZT',
    cname: '状态'
  },
  {
    colName: 'LRR',
    cname: '录入人'
  },
  {
    colName: 'CREATEDTIME',
    cname: '录入时间'
  }
]

const handleView = (SYSTEMID) => {
  window.open(`/#/ccbbForm?systemid=${SYSTEMID}&type=view`, '_blank')
}
</script>

<template>
  <HeaderTitle title="协助历史查询"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
      >
        <template #BH="prop">
          <a href="javascript:void(0)" @click="handleView(prop.row.SYSTEMID)">{{
            prop.row.SYSTEMID
          }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
