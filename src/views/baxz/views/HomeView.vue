<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'

// import QueryForm from '@/components/simpleQuery/queryForm.vue'

const handleAdd = () => {
  window.open(`/#/baxz?type=add`, '_blank')
}

const handleEdit = ({ SYSTEMID }) => {
  window.open(`/#/baxz?systemid=${SYSTEMID}&type=add`, '_blank')
}

const queryConditions = [
  {
    cname: '案件编号',
    colName: 'AJBH',
    inputType: 'string'
  },
  {
    cname: '请示协作省',
    colName: 'QSXZS',
    inputType: 'dict',
    type: 'static',
    kind: '140'
  },
  {
    cname: '录入人',
    colName: 'LRR',
    inputType: 'string'
  },
  {
    cname: '录入时间',
    colName: 'LRSJ',
    inputType: 'dateRange'
  },
  {
    cname: '协作事项',
    colName: 'XZSX',
    inputType: 'dict',
    type: 'static',
    kind: 'baxz_qqxzsx'
  },
  {
    cname: '录入人单位',
    colName: 'LRDW',
    inputType: 'dict',
    type: 'static',
    kind: '06'
  }
]

const queryId = 'B_BAXZ_INFO20241007165000'

const tableColumns = [
  {
    colName: 'BAXZBH',
    cname: '办案协助编号',
    slot: 'BH'
  },
  {
    colName: 'LRDW',
    cname: '录入单位'
  },
  {
    colName: 'SFQNXZ',
    cname: '是否区内协作'
  },
  {
    colName: 'PROVINCE',
    cname: '请求协作省'
  },
  {
    colName: 'ASSISUNIT',
    cname: '请求协作单位'
  },
  {
    colName: 'QQXZSS',
    cname: '请求协作事项'
  },
  {
    colName: 'DDRXM',
    cname: '带队人姓名'
  },
  {
    colName: 'DDRRS',
    cname: '带队人数'
  },
  {
    colName: 'TXR',
    cname: '同行人'
  },
  {
    colName: 'TXRS',
    cname: '同行人数'
  },
  {
    colName: 'XZSXRSJE',
    cname: '请求协作事项具体人数或金额'
  },
  {
    colName: 'QQXZSXSFWB',
    cname: '请求协作手续是否完备'
  },
  {
    colName: 'ZRS',
    cname: '总人数'
  },
  {
    colName: 'QQXZSJ',
    cname: '请求协作时间'
  }
]

const operations = [
  {
    name: '添加',
    type: 'primary',
    handle: handleAdd
  }
  // {
  //   name: '删除',
  //   type: 'danger',
  //   size: 'small',
  //   handle: handleDelete
  // }
]
</script>

<template>
  <HeaderTitle title="办案协作查询"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="queryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <!-- <template #cz="prop">
          <el-button size="small" @click="handleEdit(prop.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(prop.row)">删除</el-button>
        </template> -->
        <template #BH="prop">
          <a href="javascript:void(0)" @click="handleEdit(prop.row)">{{ prop.row.BAXZBH }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
