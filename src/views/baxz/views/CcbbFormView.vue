<template>
  <div class="container" v-loading="loading">
    <HeaderTitle title="出差报备表单"></HeaderTitle>
    <el-form
      ref="ruleFormRef"
      :rules="rules"
      label-width="auto"
      :model="formData"
      :inline="true"
      :disabled="isViewMode"
      style="max-width: 1600px; margin: auto; margin-top: 20px"
    >
      <table class="table">
        <tr>
          <td class="titleTd requiredTd">出差民警</td>
          <td class="inputTd">
            <el-form-item prop="ccmj">
              <DictSelect
                v-model="formData.ccmj"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">随行民警</td>
          <td class="inputTd">
            <el-form-item prop="sxmj">
              <DictSelect
                v-model="formData.sxmj"
                :showCode="false"
                type="dynamic"
                :params="{ configId: '10002' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">联系电话</td>
          <td class="inputTd">
            <el-form-item prop="lxdh">
              <el-input v-model="formData.lxdh" placeholder="请输入" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">出差事项</td>
          <td class="inputTd">
            <el-form-item prop="ccsx">
              <DictSelect
                v-model="formData.ccsx"
                :showCode="false"
                type="static"
                kind="baxz_qqxzsx"
              />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">前往地市</td>
          <td class="inputTd">
            <el-form-item prop="qwds">
              <DictSelect v-model="formData.qwds" :showCode="false" type="static" kind="ssds" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">协助单位</td>
          <td class="inputTd">
            <el-form-item prop="xzdw">
              <DictSelect v-model="formData.xzdw" :showCode="false" type="static" kind="06" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">到达时间</td>
          <td class="inputTd">
            <el-form-item prop="ddsj">
              <el-date-picker
                v-model="formData.ddsj"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择到达时间"
              />
            </el-form-item>
          </td>
          <td class="titleTd">案件编号</td>
          <td class="inputTd">
            <el-input v-model="formData.ajbh" placeholder="请输入" />
          </td>
        </tr>
        <tr>
          <td class="titleTd">附件</td>
          <td colspan="5" style="padding: 10px">
            <!-- 上传附件部分 -->
            <div style="display: flex; align-items: center; margin-bottom: 10px">
              <el-button type="primary" @click="openUploadDialog">上传附件</el-button>
              <div class="upload-hint">（必传材料：办案协作函、人民警察证复印件）</div>
              <el-button type="success" style="margin-left: auto" @click="goToIntroLetter"
                >介绍信</el-button
              >
            </div>
            <el-dialog v-model="dialogVisible" title="上传附件" width="500px">
              <el-form :model="formData2" class="upload-form">
                <el-form-item label="文件类型" prop="fileType" style="margin-bottom: 20px">
                  <DictSelect
                    v-model="formData2.wjlx"
                    :showCode="false"
                    type="static"
                    kind="ccbb_wjlx"
                  />
                </el-form-item>
                <el-form-item label="选择文件" prop="file" style="margin-bottom: 20px">
                  <el-button type="primary" v-if="!formData2.wjlx" @click="handleBeforeUpload"
                    >选择文件</el-button
                  >

                  <el-upload
                    ref="uploadRef"
                    class="upload-demo"
                    :auto-upload="false"
                    :limit="1"
                    :file-list="uploadFileList"
                    :on-change="handleFileChange"
                    :on-remove="handleRemove"
                    list-type="text"
                    accept="image/*"
                    v-else
                  >
                    <el-button type="primary">选择文件</el-button>
                  </el-upload>
                </el-form-item>
              </el-form>
            </el-dialog>
            <el-dialog v-model="previewDialogVisible" title="文件预览" width="500px">
              <img :src="previewImage" alt="文件预览" width="100%" />
            </el-dialog>

            <el-table
              header-row-class-name="headclass"
              :data="fileList"
              border
              style="width: 100%; margin-top: 10px"
              stripe
            >
              <el-table-column prop="name" label="文件名">
                <template v-slot="scope">
                  <el-link type="primary" @click="previewFile(scope.row)">
                    {{ scope.row.name }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="文件大小"></el-table-column>
              <el-table-column prop="status" label="状态"></el-table-column>
              <el-table-column prop="wjlx" label="文件类型">
                <template v-slot="scope">
                  <span v-if="scope.row.wjlx === '01'">警官证</span>
                  <span v-else-if="scope.row.wjlx === '02'">办案协助函</span>
                  <span v-else-if="scope.row.wjlx === '03'">法律文书</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template v-slot="scope">
                  <el-button
                    @click="handleRemoveFile(scope.$index)"
                    type="primary"
                    link
                    size="small"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </td>
        </tr>
        <tr>
          <td class="titleTd">录入人</td>
          <td class="inputTd">
            <el-form-item prop="lrr">
              <el-input disabled v-model="formData.lrr" placeholder="请输入" />
            </el-form-item>
          </td>
          <td class="titleTd">录入单位</td>
          <td class="inputTd">
            <el-form-item prop="lrdw">
              <el-input disabled v-model="formData.lrdw" placeholder="请输入" />
            </el-form-item>
          </td>
          <td colspan="2"></td>
        </tr>
      </table>
    </el-form>
    <div class="btn-area">
      <el-button v-if="!systemid && !isViewMode" link @click="resetForm">重置</el-button>
      <el-button v-if="!isViewMode" type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
      <el-button v-if="isViewMode" @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useCcbbStore } from '@/views/baxz/stores/ccbb.js'
import { ElMessage } from 'element-plus'
import { getNormalUrlParams } from '@/utils/common.js'
import HeaderTitle from '@/components/headerTitle/index.vue'
import DictSelect from '@/components/dict/DictSelect.vue'

const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
const params = getNormalUrlParams()
const loading = ref(false)
const dialogVisible = ref(false)
const systemid = params.systemid
const isViewMode = ref(params.type === 'view')
const previewDialogVisible = ref(false)
const previewImage = ref('')
const formData2 = ref({})
const formData = ref({})
const ruleFormRef = ref()
const ccbbStore = useCcbbStore()
const rules = reactive({
  ccmj: [{ required: true, message: '此项必填' }],
  sxmj: [{ required: true, message: '此项必填' }],
  lxdh: [{ required: true, message: '此项必填' }],
  ccsx: [{ required: true, message: '此项必填' }],
  qwds: [{ required: true, message: '此项必填' }],
  xzdw: [{ required: true, message: '此项必填' }],
  ddsj: [{ required: true, message: '此项必填' }]
})

// 上传文件列表（在对话框中使用）
const uploadFileList = ref([])
// 主页面的文件列表
const fileList = ref([])

// 引用上传组件
const uploadRef = ref(null)

const handleBeforeUpload = () => {
  ElMessage.error('请先选择文件类型')
}

const previewFile = (data) => {
  const base64Data = data.base64

  if (base64Data.startsWith('data:image/') || data.wjlx === '01' || data.wjlx === '02') {
    previewDialogVisible.value = true
    previewImage.value = base64Data.startsWith('data:image/')
      ? base64Data
      : `data:image/png;base64,${base64Data}`
  } else {
    // 如果不是图片，则下载到本地
    const link = document.createElement('a')
    link.href = URL.createObjectURL(new Blob([base64Data], { type: 'application/octet-stream' }))
    link.download = data.name
    link.click()
  }
}

const resetForm = () => {
  fileList.value = []
  formData.value = {
    lrr: userInfo.loginUserDTO.name,
    lrdw: userInfo.loginUserDTO.deptDetail
  }
}

const openUploadDialog = () => {
  uploadFileList.value = []
  formData2.value = {}
  dialogVisible.value = true
}

const goToIntroLetter = () => {
  // 构建跳转参数
  const params = {
    ajbh: formData.value.ajbh
  }

  // 将参数转换为URL查询字符串
  const queryString = Object.keys(params)
    .filter((key) => params[key])
    .map((key) => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')

  // 跳转到介绍信页面
  const url = `/#/jsx?${queryString}`
  window.open(url, '_blank')
}

const handleRemoveFile = (index) => {
  fileList.value.splice(index, 1)
  ElMessage.success('文件已成功移除')
}

// 处理文件变化（转换为 Base64）
const handleFileChange = (newFile) => {
  const wjlx = formData2.value.wjlx
  if (newFile.raw) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const base64 = e.target.result
      const newFileToServer = {
        ...newFile,
        base64: base64,
        wjlx: wjlx
      }
      // 更新文件列表中的对应文件
      uploadFileList.value.push(newFileToServer)
      fileList.value.push(newFileToServer)
      dialogVisible.value = false
    }
    reader.readAsDataURL(newFile.raw)
  }
}

// 移除上传列表中的文件
const handleRemove = (file) => {
  uploadFileList.value = uploadFileList.value.filter((item) => item.uid !== file.uid)
}

onMounted(() => {
  const params = getNormalUrlParams()
  const { ajbh } = params
  if (systemid) {
    loading.value = true
    ccbbStore.getCcbbForm({ systemid }).then((res) => {
      // 处理日期格式
      if (res.ddsj && res.ddsj.length === 10) {
        res.ddsj = res.ddsj + ' 00:00:00'
      }
      formData.value = res
      fileList.value =
        res.fjs?.map((item) => {
          return { ...item, name: item.filename, size: item.filesize, status: 'done' }
        }) || []
      loading.value = false
    })
  } else if (ajbh) {
    formData.value = {
      ajbh: ajbh,
      lrr: userInfo.loginUserDTO.name,
      lrdw: userInfo.loginUserDTO.deptDetail
    }
  } else {
    formData.value = {
      lrr: userInfo.loginUserDTO.name,
      lrdw: userInfo.loginUserDTO.deptDetail
    }
  }
})

const submitForm = (formEl) => {
  if (!formEl) return
  loading.value = true

  const myfileList = fileList.value.map((item) => {
    return {
      filename: item.name,
      base64: item.base64 ? item.base64.replace(/^data:.*;base64,/, '') : item.base64,
      wjlx: item.wjlx,
      filesize: item.size
    }
  })

  // Check if required file types are present
  const hasRequiredFiles =
    myfileList.some((item) => item.wjlx === '01') && myfileList.some((item) => item.wjlx === '02')

  if (!hasRequiredFiles) {
    ElMessage.warning('请上传相关文件 （必传材料：办案协作函、人民警察证，法律文书）')
    loading.value = false
    return
  }

  formEl.validate((valid) => {
    if (valid) {
      formData.value.fjs = myfileList

      if (formData.value.systemid) {
        ccbbStore.updateCcbbForm(formData.value).then(() => {
          ElMessage.success('修改成功')
          window.parent.location.reload()
          window.close()
        })
      } else {
        formData.value.lrdw = userInfo.loginUserDTO.dept
        ccbbStore.saveCcbbForm(formData.value).then(() => {
          window.close()
          window.parent.location.reload()
          ElMessage.success('保存成功')
        })
      }
    } else {
      loading.value = false
    }
  })
}

const handleClose = () => {
  window.close()
}
</script>

<style lang="less" scoped>
.container {
  box-sizing: border-box;

  :deep(.el-select__wrapper) {
    box-shadow: none !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none !important;
  }

  :deep(.el-input__wrapper.is-focused) {
    box-shadow: none !important;
  }

  :deep(.el-select__wrapper.is-focused) {
    box-shadow: none !important;
  }
  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 0px;
  }
  :deep(.el-form-item.is-error) {
    box-shadow: 0 0 0 1px var(--el-color-danger);
    border-radius: 4px;
  }
  :deep(.el-form-item__error) {
    top: unset;
    text-align: right;
    right: 0;
    left: unset;
    padding-right: 12px;
  }
}

.upload-form {
  :deep(.el-select__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-input__wrapper.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-select__wrapper.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }
}

.btn-area {
  padding: 24px;
  text-align: center;
}

.table {
  border-collapse: collapse;
  width: 100%;
}

.table,
.table th,
.table td {
  border: 1px solid #e0dddd;
}

.titleTd,
.inputTd {
  padding: 4px;
}
.titleTd {
  width: 180px;
  text-align: right;
  padding-right: 8px;
  background-color: #ececec;
}
.requiredTd {
  color: #ff0000;
}

.upload-hint {
  margin-left: 12px;
  color: #ff0000;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  position: relative;
  top: 1px;
}
</style>
