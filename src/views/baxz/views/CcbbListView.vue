<script setup>
import SimpleQuery from '@/components/simpleQuery/simpleQuery.vue'
import HeaderTitle from '@/components/headerTitle/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCcbbStore } from '@/views/baxz/stores/ccbb.js'
import { getNormalUrlParams } from '@/utils/common.js'
import { ref } from 'vue'

const ccbbStore = useCcbbStore()
const currentQueryId = ref('B_BAXZ_CCBB20250512165000')

const handleAdd = () => {
  //先查询URL 有没有 AJBH
  const params = getNormalUrlParams()
  const { ajbh } = params
  if (ajbh) {
    window.open(`/#/ccbbForm?type=add&ajbh=${ajbh}`, '_blank')
  } else {
    window.open(`/#/ccbbForm?type=add`, '_blank')
  }
}

const handleEdit = (SYSTEMID, QSZT_CODE) => {
  if (QSZT_CODE === '1') {
    window.open(`/#/ccbbForm?systemid=${SYSTEMID}&type=view`, '_blank')
  } else {
    window.open(`/#/ccbbForm?systemid=${SYSTEMID}&type=edit`, '_blank')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await ccbbStore.deleteCcbbForm(row.SYSTEMID)
    ElMessage.success('删除成功')
    // 刷新列表
    window.location.reload()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleHistory = () => {
  window.open(`/#/ccbbHistory`, '_blank')
}

const queryConditions = [
  {
    cname: '出差编号',
    colName: 'CCBH',
    inputType: 'string'
  },
  {
    cname: '出差民警',
    colName: 'CCMJ',
    inputType: 'string'
  },
  {
    cname: '出差事项',
    colName: 'CCSX',
    inputType: 'string'
  },
  {
    cname: '前往地市',
    colName: 'QWDS',
    inputType: 'dict',
    type: 'static',
    kind: '140'
  },
  {
    cname: '协助单位',
    colName: 'XZDW',
    inputType: 'dict',
    type: 'static',
    kind: '06'
  },
  {
    cname: '录入时间',
    colName: 'LRSJ',
    inputType: 'dateRange'
  }
]

const tableColumns = [
  {
    colName: 'SYSTEMID',
    cname: '出差编号',
    slot: 'BH'
  },
  {
    colName: 'CCMJ',
    cname: '出差民警'
  },
  {
    colName: 'SXMJ',
    cname: '随行民警'
  },
  {
    colName: 'CCSX',
    cname: '出差事项'
  },
  {
    colName: 'XZDW',
    cname: '协助单位'
  },
  {
    colName: 'DDSJ',
    cname: '到达时间'
  },
  {
    colName: 'QSZT',
    cname: '状态'
  },
  {
    colName: 'LRR',
    cname: '录入人'
  },
  {
    colName: 'CREATEDTIME',
    cname: '录入时间'
  }
]

const operations = [
  {
    name: '新增',
    type: 'primary',
    handle: handleAdd
  },
  {
    name: '协助历史',
    type: 'primary',
    handle: handleHistory
  }
]
</script>

<template>
  <HeaderTitle title="出差报备查询"></HeaderTitle>

  <main class="main">
    <div style="margin-top: 12px">
      <SimpleQuery
        :queryId="currentQueryId"
        :queryConditions="queryConditions"
        :tableColumns="tableColumns"
        :operations="operations"
      >
        <template #BH="prop">
          <a href="javascript:void(0)" @click="handleEdit(prop.row.SYSTEMID, prop.row.QSZT_CODE)">{{
            prop.row.SYSTEMID
          }}</a>
        </template>
      </SimpleQuery>
    </div>
  </main>
</template>

<style scoped>
.main {
  width: 1600px;
  padding: 12px;
  margin: 0 auto;
}
</style>
