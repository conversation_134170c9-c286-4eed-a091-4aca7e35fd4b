<template>
  <div class="container" v-loading="loading">
    <HeaderTitle title="请求协作表单"></HeaderTitle>
    <el-form
      ref="ruleFormRef"
      :rules="rules"
      label-width="auto"
      :model="formData"
      :inline="true"
      style="max-width: 1600px; margin: auto; margin-top: 20px"
    >
      <table class="table">
        <tr>
          <td class="titleTd requiredTd">请求协作单位</td>
          <td class="inputTd">
            <el-form-item prop="assisunit">
              <DictSelect
                v-model="formData.assisunit"
                :showCode="false"
                type="dynamic"
                @change="handleAssisunitChange"
                :params="{ configId: 'ztry_06' }"
              />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd">请求协作省</td>
          <td class="inputTd">
            <el-form-item prop="province">
              <DictSelect v-model="formData.province" :showCode="false" type="static" kind="140" />
            </el-form-item>
          </td>
          <td class="titleTd requiredTd"></td>
          <td class="inputTd"></td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">是否区内协作</td>
          <td class="inputTd">
            <el-form-item prop="sfqnxz">
              <DictSelect v-model="formData.sfqnxz" :showCode="false" type="static" kind="00"
            /></el-form-item>
          </td>
          <td class="titleTd requiredTd">请求协作事项</td>
          <td class="inputTd">
            <el-form-item prop="qqxzss">
              <DictSelect
                v-model="formData.qqxzss"
                :showCode="false"
                type="static"
                kind="baxz_qqxzsx"
            /></el-form-item>
          </td>
          <td class="titleTd requiredTd">带队人姓名</td>
          <td class="inputTd">
            <el-form-item prop="ddrxm">
              <el-input v-model="formData.ddrxm" placeholder="请输入" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">带队人数</td>
          <td class="inputTd">
            <el-form-item prop="ddrrs">
              <el-input v-model="formData.ddrrs" placeholder="请输入"
            /></el-form-item>
          </td>
          <td class="titleTd requiredTd">同行人</td>
          <td class="inputTd">
            <el-form-item prop="txr">
              <el-input v-model="formData.txr" placeholder="请输入"
            /></el-form-item>
          </td>
          <td class="titleTd requiredTd">同行人数</td>
          <td class="inputTd">
            <el-form-item prop="txrs">
              <el-input v-model="formData.txrs" placeholder="请输入"
            /></el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd">案件编号</td>
          <td class="inputTd">
            <el-form-item prop="ajbh">
              <el-input v-model="formData.ajbh" placeholder="请输入"
            /></el-form-item>
          </td>
          <td class="titleTd">案件名称</td>
          <td class="inputTd" colspan="3">
            <el-form-item prop="ajmc">
              <el-input v-model="formData.ajmc" placeholder="请输入"
            /></el-form-item>
          </td>
        </tr>
        <tr>
          <td class="titleTd requiredTd">请求协作时间</td>
          <td class="inputTd">
            <el-form-item prop="xzsxrsje">
              <el-date-picker
                v-model="formData.qqxzsj"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择请求协作时间"
            /></el-form-item>
          </td>
          <!-- <td class="titleTd">请求协作手续是否完备</td> -->
          <td colspan="5"></td>
          <!-- <td class="inputTd">
            <el-form-item prop="qqxzsxsfwb">
              <DictSelect v-model="formData.qqxzsxsfwb" :showCode="false" type="static" kind="00" />
            /></el-form-item>
          </td> -->
        </tr>
        <tr>
          <td class="titleTd">请求协作事项具体人数或金额</td>
          <td class="inputTd" colspan="5">
            <el-form-item prop="xzsxrsje">
              <el-input v-model="formData.xzsxrsje" placeholder="请输入"
            /></el-form-item>
          </td>
        </tr>

        <tr>
          <td class="titleTd">附件</td>
          <td colspan="5" style="padding: 10px">
            <!-- 上传附件部分 -->
            <div style="display: flex; align-items: center; margin-bottom: 10px">
              <el-button type="primary" @click="openUploadDialog">上传附件</el-button>
              <div class="upload-hint">（必传材料：法律文书、人民警察证复印件）</div>
            </div>
            <el-dialog v-model="dialogVisible" title="上传附件" width="500px">
              <el-form :model="formData2" class="upload-form">
                <el-form-item label="文件类型" prop="fileType" style="margin-bottom: 20px">
                  <DictSelect
                    v-model="formData2.wjlx"
                    :showCode="false"
                    type="static"
                    kind="baxz_wjlx"
                  />
                </el-form-item>
                <el-form-item label="选择文件" prop="file" style="margin-bottom: 20px">
                  <el-button type="primary" v-if="!formData2.wjlx" @click="handleBeforeUpload"
                    >选择文件</el-button
                  >

                  <el-upload
                    ref="uploadRef"
                    class="upload-demo"
                    :auto-upload="false"
                    :limit="1"
                    :file-list="uploadFileList"
                    :on-change="handleFileChange"
                    :on-remove="handleRemove"
                    list-type="text"
                    accept="image/*"
                    v-else
                  >
                    <el-button type="primary">选择文件</el-button>
                  </el-upload>
                </el-form-item>
              </el-form>
            </el-dialog>
            <el-dialog v-model="previewDialogVisible" title="文件预览" width="500px">
              <img :src="previewImage" alt="文件预览" width="100%" />
            </el-dialog>
            <el-table
              header-row-class-name="headclass"
              :data="fileList"
              border
              style="width: 100%; margin-top: 10px"
              stripe
            >
              <el-table-column prop="name" label="文件名">
                <template v-slot="scope">
                  <el-link type="primary" @click="previewFile(scope.row)">
                    {{ scope.row.name }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="文件大小"></el-table-column>
              <el-table-column prop="status" label="状态"></el-table-column>
              <el-table-column prop="wjlx" label="文件类型">
                <template v-slot="scope">
                  <span v-if="scope.row.wjlx === '01'">警官证</span>
                  <span v-else-if="scope.row.wjlx === '02'">办协助函</span>
                  <span v-else-if="scope.row.wjlx === '03'">强制措施文书</span>
                  <span v-else-if="scope.row.wjlx === '99'">其他</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template v-slot="scope">
                  <el-button @click="handleExportFile(scope.row)" type="success" link size="small"
                    >导出</el-button
                  >
                  <el-button
                    @click="handlePrintSingleFile(scope.row)"
                    type="warning"
                    link
                    size="small"
                    >打印</el-button
                  >
                  <el-button
                    @click="handleRemoveFile(scope.$index)"
                    type="primary"
                    link
                    size="small"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </td>
        </tr>
        <tr>
          <td class="titleTd">录入人</td>
          <td class="inputTd">
            <el-form-item prop="lrr">
              <el-input disabled v-model="formData.lrr" placeholder="请输入"
            /></el-form-item>
          </td>
          <td class="titleTd">录入单位</td>
          <td class="inputTd">
            <el-form-item prop="lldw">
              <el-input disabled v-model="formData.lrdw" placeholder="请输入"
            /></el-form-item>
          </td>
          <td colspan="2"></td>
        </tr>
      </table>
    </el-form>
    <div class="btn-area">
      <el-button v-if="!systemid" link @click="resetForm">重置</el-button>
      <el-button type="success" @click="handlePrintAttachments">打印附件</el-button>
      <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
    </div>
    <!-- -->
  </div>
</template>

<style scoped>
.form-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}
</style>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useBazxStore } from '@/views/baxz/stores/baxz.js'
import { ElMessage } from 'element-plus'
import { getNormalUrlParams } from '@/utils/common.js'
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
console.log('userInfo', userInfo)
const params = getNormalUrlParams()
const loading = ref(false)
const dialogVisible = ref(false)
const systemid = params.systemid
const previewDialogVisible = ref(false)
const previewImage = ref('')
const formData2 = ref({})
// const sfCode = ref('45')
const formData = ref({})
const ruleFormRef = ref()
const bazxStore = useBazxStore()
const rules = reactive({
  province: [{ required: true, message: '此项必填' }],
  qqxzss: [{ required: true, message: '此项必填' }],
  assisunit: [{ required: true, message: '此项必填' }],
  // city: [{ required: true, message: '此项必填' }],
  sfqnxz: [{ required: true, message: '此项必填' }],
  ddrxm: [{ required: true, message: '此项必填' }],
  qqxzsj: [{ required: true, message: '此项必填' }],
  txr: [{ required: true, message: '此项必填' }],
  txrs: [{ required: true, message: '此项必填' }]
})
// 附件列表（用于表格展示和表单提交）
// const attachments = ref([])
// 上传文件列表（在对话框中使用）
const uploadFileList = ref([])
// 主页面的文件列表
const fileList = ref([])

// 引用上传组件
const uploadRef = ref(null)
// 处理上传前的验证
// const handleBeforeUpload = () => {
//   console.log('formData2.value.wjlx', formData2.value.wjlx)

//   if (!formData2.value.wjlx) {
//     ElMessage.error('请先选择文件类型')
//     return false
//   }
//   return true
// }

const handleBeforeUpload = () => {
  ElMessage.error('请先选择文件类型')
}
const previewFile = (data) => {
  console.log(data)
  const base64Data = data.base64

  // 检查是否为图片文件：base64以data:image/开头，或者文件类型为01/02，或者文件名包含图片格式后缀
  const isImageFile =
    base64Data.startsWith('data:image/') ||
    data.wjlx === '01' ||
    data.wjlx === '02' ||
    /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(data.filename || data.name)

  if (isImageFile) {
    previewDialogVisible.value = true
    previewImage.value = base64Data.startsWith('data:image/')
      ? base64Data
      : `data:image/png;base64,${base64Data}`
  } else {
    // 如果不是图片，则下载到本地
    const link = document.createElement('a')
    link.href = URL.createObjectURL(new Blob([base64Data], { type: 'application/octet-stream' }))
    link.download = data.name
    link.click()
  }
}

const resetForm = () => {
  fileList.value = []
  formData.value = {
    lrr: userInfo.loginUserDTO.name,
    lrdw: userInfo.loginUserDTO.deptDetail
  }
}

const openUploadDialog = () => {
  uploadFileList.value = []
  formData2.value = {}
  dialogVisible.value = true
}

const handleRemoveFile = (index) => {
  fileList.value.splice(index, 1)
  ElMessage.success('文件已成功移除')
}

// 处理文件导出
const handleExportFile = (file) => {
  try {
    const base64Data = file.base64

    // 检查是否为图片文件
    const isImageFile =
      base64Data.startsWith('data:image/') ||
      file.wjlx === '01' ||
      file.wjlx === '02' ||
      /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file.filename || file.name)

    let blob

    if (isImageFile) {
      // 图片文件处理
      if (base64Data.startsWith('data:image/')) {
        blob = dataURLtoBlob(base64Data)
      } else {
        blob = base64ToBlob(base64Data, 'image/png')
      }
    } else {
      // 其他文件类型
      blob = base64ToBlob(base64Data, 'application/octet-stream')
    }

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = file.name || file.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('文件导出成功')
  } catch (error) {
    console.error('文件导出失败:', error)
    ElMessage.error('文件导出失败')
  }
}

// 将 base64 转换为 Blob
const base64ToBlob = (base64, mimeType) => {
  const byteCharacters = atob(base64)
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: mimeType })
}

// 将 data URL 转换为 Blob
const dataURLtoBlob = (dataURL) => {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

// 处理打印附件功能
const handlePrintAttachments = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('暂无附件可打印')
    return
  }

  // 过滤出图片文件
  const imageFiles = fileList.value.filter((file) => {
    const base64Data = file.base64
    return (
      base64Data.startsWith('data:image/') ||
      file.wjlx === '01' ||
      file.wjlx === '02' ||
      /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file.filename || file.name)
    )
  })

  if (imageFiles.length === 0) {
    ElMessage.warning('暂无图片附件可打印')
    return
  }

  // 创建打印内容
  const printContent = generateAttachmentPrintContent(imageFiles)

  // 创建打印窗口
  const printWindow = window.open('', '_blank', 'width=800,height=600')
  printWindow.document.write(printContent)
  printWindow.document.close()

  // 等待图片加载完成后打印
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
  }, 1000)
}

// 处理单个文件打印功能
const handlePrintSingleFile = (file) => {
  const base64Data = file.base64

  // 检查是否为图片文件
  const isImageFile =
    base64Data.startsWith('data:image/') ||
    file.wjlx === '01' ||
    file.wjlx === '02' ||
    /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file.filename || file.name)

  if (!isImageFile) {
    ElMessage.warning('该文件不是图片，无法打印')
    return
  }

  // 创建单个文件打印内容
  const printContent = generateSingleFilePrintContent(file)

  // 创建打印窗口
  const printWindow = window.open('', '_blank', 'width=800,height=600')
  printWindow.document.write(printContent)
  printWindow.document.close()

  // 等待图片加载完成后打印
  setTimeout(() => {
    printWindow.print()
    printWindow.close()
  }, 1000)
}

// 生成单个文件打印内容
const generateSingleFilePrintContent = (file) => {
  const imageSrc = file.base64.startsWith('data:image/')
    ? file.base64
    : `data:image/png;base64,${file.base64}`

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>附件打印</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          margin: 20px;
          font-size: 14px;
          line-height: 1.6;
        }
        .attachment-item {
          margin-bottom: 20px;
        }
        .attachment-image {
          text-align: center;
        }
        .attachment-image img {
          max-width: 100%;
          height: auto;
        }
        @media print {
          body { margin: 10px; }
          .attachment-item { 
            page-break-inside: avoid; 
          }
          .attachment-image img {
            max-height: 80vh;
            width: auto;
          }
        }
      </style>
    </head>
    <body>
      <div class="attachment-item" style="page-break-inside: avoid; margin-bottom: 30px;">
        <div class="attachment-image">
          <img src="${imageSrc}" alt="${file.name || file.filename}" style="max-width: 100%; height: auto;">
        </div>
      </div>
    </body>
    </html>
  `
}

// 生成附件打印内容
const generateAttachmentPrintContent = (imageFiles) => {
  const imageHtml = imageFiles
    .map((file) => {
      const imageSrc = file.base64.startsWith('data:image/')
        ? file.base64
        : `data:image/png;base64,${file.base64}`

      return `
      <div class="attachment-item" style="page-break-inside: avoid; margin-bottom: 30px;">
        <div class="attachment-image">
          <img src="${imageSrc}" alt="${file.name || file.filename}" style="max-width: 100%; height: auto;">
        </div>
      </div>
    `
    })
    .join('')

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>附件打印</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          margin: 20px;
          font-size: 14px;
          line-height: 1.6;
        }
        .print-header {
          text-align: center;
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
        }
        .attachment-item {
          margin-bottom: 20px;
        }
        .attachment-image {
          text-align: center;
        }
        .attachment-image img {
          max-width: 100%;
          height: auto;
        }
        .print-footer {
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #eee;
          padding-top: 10px;
        }
        @media print {
          body { margin: 10px; }
          .attachment-item { 
            page-break-inside: avoid; 
            border: 1px solid #000;
          }
          .attachment-image img {
            max-height: 80vh;
            width: auto;
          }
        }
      </style>
    </head>
    <body>
      
      ${imageHtml}
      
      
    </body>
    </html>
  `
}

// 处理文件变化（转换为 Base64）
const handleFileChange = (newFile) => {
  const wjlx = formData2.value.wjlx
  if (newFile.raw) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const base64 = e.target.result
      const newFileToServer = {
        ...newFile,
        base64: base64,
        wjlx: wjlx
      }
      // 更新文件列表中的对应文件
      uploadFileList.value.push(newFileToServer)
      fileList.value.push(newFileToServer)
      dialogVisible.value = false
    }
    reader.readAsDataURL(newFile.raw)
  }
}

// 移除上传列表中的文件
const handleRemove = (file) => {
  uploadFileList.value = uploadFileList.value.filter((item) => item.uid !== file.uid)
}

// const handleProvinceChange = (value) => {
//   if (value === '45') {
//     formData.value.sfqnxz = '1'
//     sfCode.value = '45'
//   } else {
//     formData.value.sfqnxz = '0'
//     sfCode.value = '00'
//   }
// }

const handleAssisunitChange = (value) => {
  if (value && value.length >= 2) {
    let province = value.substring(0, 2)
    if (province === '01') {
      province = '11'
    }
    // 取value前两位，设置给province
    formData.value.province = province
  } else {
    // 如果value无效则清空province
    formData.value.province = ''
  }
}

onMounted(() => {
  if (systemid) {
    loading.value = true
    bazxStore.getBaxzForm({ systemid }).then((res) => {
      console.log('res', res)
      formData.value = res
      fileList.value = res.fjs?.map((item) => {
        return { ...item, name: item.filename, size: item.filesize, status: 'done' }
      })
      loading.value = false
    })
  } else {
    formData.value = {
      lrr: userInfo.loginUserDTO.name,
      lrdw: userInfo.loginUserDTO.deptDetail
    }
  }
})

const submitForm = (formEl) => {
  if (!formEl) return
  loading.value = true

  const myfileList = fileList.value.map((item) => {
    return {
      filename: item.name,
      base64: item.base64 ? item.base64.replace(/^data:.*;base64,/, '') : item.base64,
      wjlx: item.wjlx,
      filesize: item.size
    }
  })

  console.log('myfileList', fileList)

  // Check if required file types are present
  const hasRequiredFiles =
    myfileList.some((item) => item.wjlx === '01') && myfileList.some((item) => item.wjlx === '02')

  if (!hasRequiredFiles) {
    ElMessage.warning('请上传相关文件 （必传材料：法律文书、人民警察证复印件）')
    loading.value = false
    return
  }

  formEl.validate((valid) => {
    if (valid) {
      formData.value.fjs = myfileList

      if (formData.value.systemid) {
        bazxStore.updateBaxzForm(formData.value).then(() => {
          loading.value = false
          ElMessage.success('修改成功')
          // Refresh parent page data
          window.parent.location.reload()
        })
      } else {
        formData.value.lrdw = userInfo.loginUserDTO.dept
        bazxStore.saveBaxzForm(formData.value).then((res) => {
          loading.value = false
          ElMessage.success('保存成功')
          // 更新 formData 的 systemid，避免重复保存都是新增
          formData.value.systemid = res.systemid
          // Refresh parent page data
          window.parent.location.reload()
        })
      }
    } else {
      console.log('error submit!')
      loading.value = false
    }
  })
}
</script>

<style lang="less" scoped>
.container {
  // padding: 48px 64px 0px;
  box-sizing: border-box;

  :deep(.el-select__wrapper) {
    box-shadow: none !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none !important;
  }

  :deep(.el-input__wrapper.is-focused) {
    box-shadow: none !important;
  }

  :deep(.el-select__wrapper.is-focused) {
    box-shadow: none !important;
  }
  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 0px;
  }
  :deep(.el-form-item.is-error) {
    box-shadow: 0 0 0 1px var(--el-color-danger);
    border-radius: 4px;
    // margin-bottom: 18px;
  }
  :deep(.el-form-item__error) {
    top: unset;
    text-align: right;
    right: 0;
    left: unset;
    padding-right: 12px;
  }
}
// :deep(.headclass) {
//   th.el-table__cell {
//     background-color: #e0f7fa;
//   }
// }

.upload-form {
  :deep(.el-select__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-input__wrapper.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }

  :deep(.el-select__wrapper.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
  }
}

.btn-area {
  padding: 24px;
  text-align: center;
}

/* 在你的 CSS 文件中添加 */
.table {
  border-collapse: collapse;
  width: 100%;
}

.table,
.table th,
.table td {
  border: 1px solid #e0dddd;
}

.titleTd,
.inputTd {
  padding: 4px;
}
.titleTd {
  width: 180px;
  text-align: right;
  padding-right: 8px;
  background-color: #ececec;
}
.requiredTd {
  color: #ff0000;
}

.upload-hint {
  margin-left: 12px;
  color: #ff0000;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  position: relative;
  top: 1px; // 微调垂直对齐
}
</style>
