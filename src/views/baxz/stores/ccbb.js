import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as ccbbService from '@/views/baxz/services/ccbb.js'

export const useCcbbStore = defineStore('ccbb', () => {
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const dataList = ref([])
  const dialogVisible = ref(false)
  const viewId = ref('')
  const ccbbForm = ref({})

  /**
   * 保存出差报备表单
   */
  const saveCcbbForm = async (data) => {
    try {
      loading.value = true
      const res = await ccbbService.saveCcbbForm(data)
      return res
    } catch (error) {
      console.error('保存出差报备表单失败', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新出差报备表单
   */
  const updateCcbbForm = async (data) => {
    try {
      loading.value = true
      const res = await ccbbService.updateCcbbForm(data)
      return res
    } catch (error) {
      console.error('更新出差报备表单失败', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取出差报备表单
   */
  const getCcbbForm = async ({ systemid }) => {
    try {
      loading.value = true
      const res = await ccbbService.getCcbbForm({ systemid })
      ccbbForm.value = res
      return res
    } catch (error) {
      console.error('获取出差报备表单失败', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除出差报备表单
   */
  const deleteCcbbForm = async (systemid) => {
    try {
      loading.value = true
      const res = await ccbbService.deleteCcbbForm(systemid)
      return res
    } catch (error) {
      console.error('删除出差报备表单失败', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 签收出差报备表单
   */
  const signCcbbForm = async (systemid) => {
    try {
      loading.value = true
      const res = await ccbbService.signCcbbForm(systemid)
      return res
    } catch (error) {
      console.error('签收出差报备表单失败', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    currentPage,
    pageSize,
    total,
    dataList,
    dialogVisible,
    viewId,
    ccbbForm,
    saveCcbbForm,
    updateCcbbForm,
    getCcbbForm,
    deleteCcbbForm,
    signCcbbForm
  }
})
