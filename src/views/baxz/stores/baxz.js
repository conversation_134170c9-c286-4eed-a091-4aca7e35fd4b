import { ref } from 'vue'
import { defineStore } from 'pinia'
import * as bazxService from '@/views/baxz/services/baxz.js'

export const useBazxStore = defineStore('bazx', () => {
  const baxzForm = ref({})
  const saveBaxzForm = async (data) => {
    const res = await bazxService.saveBaxzForm(data)
    return res
  }

  const updateBaxzForm = async (data) => {
    const res = await bazxService.updateBaxzForm(data)
    return res
  }

  const getBaxzForm = async ({ systemid }) => {
    const res = await bazxService.getBaxzForm({ systemid })
    baxzForm.value = res
    return res
  }

  return { baxzForm, saveBaxzForm, getBaxzForm, updateBaxzForm }
})
