import { useRequest } from '@/utils/request/useRequest.js'

const { get, post, put } = useRequest()

export const getBaxzForm = async ({ systemid }) => {
  return await get(`/api/b_baxz_info/${systemid}`)
}

export const saveBaxzForm = async (data) => {
  return await post(`/api/b_baxz_info`, data)
}

export const updateBaxzForm = async (data) => {
  return await put(`/api/b_baxz_info/${data.systemid}`, data)
}
