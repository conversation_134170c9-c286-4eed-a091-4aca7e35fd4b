import { useRequest } from '@/utils/request/useRequest.js'

const { get, post, put, _delete } = useRequest()

export const getCcbbForm = async ({ systemid }) => {
  return await get(`/api/b_baxz_ccbb/${systemid}`)
}

export const saveCcbbForm = async (data) => {
  return await post(`/api/b_baxz_ccbb`, data)
}

export const updateCcbbForm = async (data) => {
  return await put(`/api/b_baxz_ccbb/${data.systemid}`, data)
}

export const deleteCcbbForm = async (systemid) => {
  return await _delete(`/api/b_baxz_ccbb/${systemid}`)
}

export const signCcbbForm = async (systemid) => {
  return await put(`/api/b_baxz_ccbb/qs/${systemid}`)
}
