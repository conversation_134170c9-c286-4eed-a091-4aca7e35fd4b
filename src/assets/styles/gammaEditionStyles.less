/* 系统主题颜色 */
@themeColor: #1890ff;
@themeSecondColor: #11d8ee;
/* 系统背景 */
@system-background-color: #f5f5f5;
/* 左侧菜单主题属性 */
@menu-background-color: rgba(0, 110, 225, 0.75);
@menu-selected-color: rgba(0, 110, 225, 1);
@menu-li-hover-color: rgba(0, 110, 225, 1);
@menu-active-color: #1e59d6;
@menu-activeSubMenu-background: linear-gradient(
    90deg,
    rgba(224, 237, 252, 1) 0%,
    rgba(255, 255, 255, 1) 91%,
    rgba(255, 255, 255, 1) 100%
  )
  rgba(225, 238, 253, 1);
/* 首页-我的关注 */
@rightConItemActive-color: rgba(5, 122, 255, 1);
@tabBorder-background: rgba(5, 122, 255, 1);
@gzItemBorder-border-color: rgba(104, 173, 248, 1);
/* 首页-消息提醒 */
@iconCon-background: #4b94ca;
/* 首页-底部按钮 */
@itemIcon-color: #5da7de;
/* 主页-标签 */
@page-tabs-activeColor: #1890ff;
