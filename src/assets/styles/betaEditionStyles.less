/* 系统主题颜色 */
@themeColor: #037079;
@themeSecondColor: #04eaff;
/* 系统背景 */
@system-background-color: #f5f5f5;
/* 左侧菜单主题属性 */
@menu-background-color: rgba(6, 106, 86, 0.75);
@menu-selected-color: rgba(6, 106, 86, 1);
@menu-li-hover-color: rgba(6, 106, 86, 1);
@menu-active-color: #066a56;
@menu-activeSubMenu-background: linear-gradient(
    90deg,
    rgba(220, 255, 255, 1) 0%,
    rgba(255, 255, 255, 1) 91%,
    rgba(255, 255, 255, 1) 100%
  )
  rgba(225, 238, 253, 1);
/* 首页-我的关注 */
@rightConItemActive-color: #00bfb3;
@tabBorder-background: #00bfb3;
@gzItemBorder-border-color: #00bfb3;
/* 首页-消息提醒 */
@iconCon-background: #00bfb3;
/* 首页-底部按钮 */
@itemIcon-color: #00bfb3;
/* 主页-标签 */
@page-tabs-activeColor: #037079;

:root {
  --el-color-primary: #037079;
}
