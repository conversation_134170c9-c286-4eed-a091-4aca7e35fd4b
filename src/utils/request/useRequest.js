import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import service from './request'

/**
 * useRequest 是一个自定义的请求钩子函数，用于封装和管理HTTP请求。
 * @param {Object} options 配置对象，可选参数，用于自定义请求行为。
 * @returns 返回一个对象，包含请求的loading状态、错误信息及get、post等方法。
 */
export function useRequest(options = {}) {
  const loading = ref(false)
  const error = ref(null)

  /// 错误处理函数
  const handleError = (err, options) => {
    console.log(err)
    let errorMessage = err.response?.data?.message || err.message
    error.value = errorMessage

    if (options?.error) {
      options.error(err)
    } else {
      switch (err.response?.status) {
        case 401:
          sessionStorage.removeItem('access_token')
          location.href = '/#/login'
          break
        case 403:
          ElMessage.error('403 没有权限')
          break
        case 404:
          ElMessage.error('404 服务路径不正确或丢失')
          break
        case 500:
          ElMessage.error('500 服务器错误')
          break
        default:
          ElMessage.error(errorMessage)
      }
    }
    return Promise.reject(err)
  }

  // 默认的请求loading显示方法
  const showLoading = () => {
    loading.value = true
  }

  // 默认的请求loading隐藏方法
  const hideLoading = () => {
    loading.value = false
  }

  // 封装请求方法
  const request = async (method, url, data, config = {}) => {
    try {
      showLoading()
      const response = await service.request({
        method,
        url,
        params: method === 'get' ? data : {}, // 如果是 GET 请求，则将数据作为参数传递
        data,
        ...config
      })
      hideLoading()
      return response
    } catch (err) {
      hideLoading()
      console.log('err', err)
      error.value = err
      handleError(err)
      return Promise.reject(err)
    }
  }

  // 返回封装好的请求函数和状态
  return {
    loading,
    error,
    get: (url, params, config) => request('get', url, params, config),
    post: (url, data, config) => request('post', url, data, config),
    put: (url, data, config) => request('put', url, data, config),
    _delete: (url, data, config) => request('delete', url, data, config)
  }
}
