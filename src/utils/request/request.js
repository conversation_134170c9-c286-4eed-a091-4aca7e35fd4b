// request.js
import axios from 'axios'
import { requestConfig } from './config'
import router from '@/router'
import { ElMessage } from 'element-plus'
// 创建一个可配置的axios实例
const service = axios.create(requestConfig)

// 在请求发送前的拦截器中处理loading和token
service.interceptors.request.use(
  (config) => {
    // 获取并添加token到请求头
    const token = getToken()

    // 如果有Token,则添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (err) => {
    return Promise.reject(err)
  }
)

// 在响应返回后的拦截器中处理loading和错误信息
service.interceptors.response.use(
  (response) => {
    // 对于成功的HTTP响应，直接返回数据
    if (response.status >= 200 && response.status < 300) {
      return response.data
    } // 特殊处理401 Unauthorized
    else if (response.status === 401) {
      // 清除用户登录信息（如token）
      sessionStorage.removeItem('token')
      // 路由重定向到登录页面
      router.push('/login')
    } else if (response.status === 500) {
      ElMessage.error('服务器错误,请联系管理员')
    }
    // 对于其他非2xx状态码，也可以按照类似方式处理
    return Promise.reject(response)
  },
  (err) => {
    return Promise.reject(err)
  }
)

function getToken() {
  return sessionStorage.getItem('token')
}

export default service
