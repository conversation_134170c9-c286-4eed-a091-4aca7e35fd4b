/**
 * SM4加密工具
 * 注意：这是一个简化的实现，实际项目中建议使用专业的加密库
 */

// 简单的SM4加密实现（仅用于测试）
// 实际项目中应该使用专业的SM4加密库，如 sm-crypto 或 gm-crypto

/**
 * 将字符串转换为十六进制
 */
function stringToHex(str) {
  let hex = '';
  for (let i = 0; i < str.length; i++) {
    hex += str.charCodeAt(i).toString(16).padStart(2, '0');
  }
  return hex;
}

/**
 * 将十六进制转换为字符串
 */
function hexToString(hex) {
  let str = '';
  for (let i = 0; i < hex.length; i += 2) {
    str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
  }
  return str;
}

/**
 * 简单的XOR加密（模拟SM4加密）
 * 注意：这不是真正的SM4算法，仅用于演示
 * 实际使用时需要替换为真正的SM4加密算法
 */
function simpleEncrypt(data, key = 'defaultkey123456') {
  // 确保密钥长度为16字节
  const normalizedKey = key.padEnd(16, '0').substring(0, 16);
  
  let result = '';
  for (let i = 0; i < data.length; i++) {
    const dataChar = data.charCodeAt(i);
    const keyChar = normalizedKey.charCodeAt(i % normalizedKey.length);
    const encrypted = dataChar ^ keyChar;
    result += encrypted.toString(16).padStart(2, '0');
  }
  
  return result;
}

/**
 * 简单的XOR解密（模拟SM4解密）
 */
function simpleDecrypt(encryptedHex, key = 'defaultkey123456') {
  // 确保密钥长度为16字节
  const normalizedKey = key.padEnd(16, '0').substring(0, 16);
  
  let result = '';
  for (let i = 0; i < encryptedHex.length; i += 2) {
    const encryptedByte = parseInt(encryptedHex.substr(i, 2), 16);
    const keyChar = normalizedKey.charCodeAt((i / 2) % normalizedKey.length);
    const decrypted = encryptedByte ^ keyChar;
    result += String.fromCharCode(decrypted);
  }
  
  return result;
}

/**
 * Base64编码
 */
function base64Encode(str) {
  return btoa(unescape(encodeURIComponent(str)));
}

/**
 * Base64解码
 */
function base64Decode(str) {
  return decodeURIComponent(escape(atob(str)));
}

/**
 * SM4加密主函数
 * @param {string} data - 要加密的数据
 * @param {string} key - 加密密钥（可选，默认使用内置密钥）
 * @param {string} mode - 输出模式：'hex' 或 'base64'
 * @returns {string} 加密后的字符串
 */
export function sm4Encrypt(data, key = 'defaultkey123456', mode = 'hex') {
  try {
    // 使用简单加密算法（实际项目中应替换为真正的SM4算法）
    const encrypted = simpleEncrypt(data, key);
    
    if (mode === 'base64') {
      // 将十六进制转换为字符串，然后进行Base64编码
      const hexString = hexToString(encrypted);
      return base64Encode(hexString);
    }
    
    return encrypted;
  } catch (error) {
    console.error('SM4加密失败:', error);
    throw error;
  }
}

/**
 * SM4解密主函数
 * @param {string} encryptedData - 要解密的数据
 * @param {string} key - 解密密钥（可选，默认使用内置密钥）
 * @param {string} mode - 输入模式：'hex' 或 'base64'
 * @returns {string} 解密后的字符串
 */
export function sm4Decrypt(encryptedData, key = 'defaultkey123456', mode = 'hex') {
  try {
    let hexData = encryptedData;
    
    if (mode === 'base64') {
      // 先进行Base64解码，然后转换为十六进制
      const decodedString = base64Decode(encryptedData);
      hexData = stringToHex(decodedString);
    }
    
    // 使用简单解密算法（实际项目中应替换为真正的SM4算法）
    return simpleDecrypt(hexData, key);
  } catch (error) {
    console.error('SM4解密失败:', error);
    throw error;
  }
}

/**
 * 生成测试用的加密字符串
 * @param {string} name - 姓名
 * @param {string} phone - 手机号
 * @param {string} idCard - 身份证号
 * @param {string} key - 加密密钥（可选）
 * @returns {string} 加密后的字符串
 */
export function generateTestSM4Code(name, phone, idCard, key = 'defaultkey123456') {
  const data = `${name},${phone},${idCard}`;
  return sm4Encrypt(data, key, 'hex');
}

// 默认导出
export default {
  encrypt: sm4Encrypt,
  decrypt: sm4Decrypt,
  generateTestCode: generateTestSM4Code
};
