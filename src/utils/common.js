export function getUrlParams() {
  const params = {};
  const urlParams = new URLSearchParams(window.location.href.split('?')[1]);

  for (const [key, value] of urlParams) {
    params[key.toUpperCase()] = value;
  }

  return params;
}


export function getNormalUrlParams() {
  const params = {};
  const urlParams = new URLSearchParams(window.location.href.split('?')[1]);

  for (const [key, value] of urlParams) {
    params[key] = value;
  }

  return params;
}
