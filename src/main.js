import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import 'dayjs/locale/zh-cn'
// If you want to use ElMessage, import it.
import 'element-plus/theme-chalk/src/message.scss'
import '@/assets/styles/themes.less'
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
// app.use(ElementPlus)
app.mount('#app')
