import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getUserInfo } from '@/services/auth'

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false)
  const userInfo = ref({})

  const getUserInfoByToken = async (token) => {
    // 获取用户信息
    const _userInfo = await getUserInfo(token)
    if (_userInfo?.loginUserDTO?.access_token) {
      isAuthenticated.value = true
    }
    // 存储用户信息
    userInfo.value = _userInfo
    console.log('token', token)
    sessionStorage.setItem('token', token)
    sessionStorage.setItem('userInfo', JSON.stringify(_userInfo));
  }

  const clearUserInfo = () => {
    isAuthenticated.value = false
    userInfo.value = {}
    sessionStorage.removeItem('userInfo')
  }

  return { isAuthenticated, clearUserInfo, userInfo, getUserInfoByToken }
})
