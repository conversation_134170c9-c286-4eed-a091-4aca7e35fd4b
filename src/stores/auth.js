import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getUserInfo, getTokenBySM4 } from '@/services/auth'

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false)
  const userInfo = ref({})

  const getUserInfoByToken = async (token) => {
    // 获取用户信息
    const _userInfo = await getUserInfo(token)
    if (_userInfo?.loginUserDTO?.access_token) {
      isAuthenticated.value = true
    }
    // 存储用户信息
    userInfo.value = _userInfo
    console.log('token', token)
    sessionStorage.setItem('token', token)
    sessionStorage.setItem('userInfo', JSON.stringify(_userInfo))
  }

  const getUserInfoBySM4Code = async (sm4Code) => {
    try {
      // 先通过SM4代码获取token
      const tokenResponse = await getTokenBySM4(sm4Code)
      console.log('tokenResponse', tokenResponse)

      // 从响应中提取token（根据实际API响应结构调整）
      const token = tokenResponse?.token || tokenResponse?.data?.token || tokenResponse

      if (token) {
        // 使用获取到的token获取用户信息
        await getUserInfoByToken(token)
      } else {
        throw new Error('未能获取到有效的token')
      }
    } catch (error) {
      console.error('通过SM4代码获取用户信息失败:', error)
      throw error
    }
  }

  const clearUserInfo = () => {
    isAuthenticated.value = false
    userInfo.value = {}
    sessionStorage.removeItem('userInfo')
  }

  return { isAuthenticated, clearUserInfo, userInfo, getUserInfoByToken, getUserInfoBySM4Code }
})
