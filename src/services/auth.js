import { useRequest } from '@/utils/request/useRequest'
const { loading, get } = useRequest()

const getUserInfo = async (token) => {
  const response = await get(
    '/api/loginInfo',
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  )
  return response
}

const getTokenBySM4 = async (sm4Code) => {
  const response = await get(`${window.config.ajblContextPath}/api/outsys/getTokenBySM4/${sm4Code}`)
  return response
}

export { getUserInfo, getTokenBySM4 }
